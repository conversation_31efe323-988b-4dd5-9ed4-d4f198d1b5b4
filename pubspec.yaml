name: shop_chill_app
description: A new Flutter project.

publish_to: 'none' 
version: 1.2.8

environment:
  sdk: '>=3.8.1 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.0
  auto_size_text: 3.0.0
  badges: ^3.1.2
  built_collection: 5.1.1
  built_value: ^8.4.1
  firebase_core: ^3.14.0
  cloud_firestore: ^5.4.2
  equatable: ^2.0.5
  expandable: 5.0.1
  firebase_auth: ^5.6.0
  floating_bottom_navigation_bar: 1.5.2
  flutter_facebook_auth: ^7.1.1
  flutter_rating_bar: 4.0.1
  http: ^1.2.2
  flutter_svg: ^2.0.10+1
  google_fonts: ^6.2.1
  google_sign_in: ^6.2.1
  google_sign_in_android: ^6.1.1
  google_sign_in_ios: ^5.5.0
  google_sign_in_platform_interface: ^2.3.0
  google_sign_in_web: ^0.12.4+2
  # intl: ^0.19.0
  intl: ^0.20.2
  json_path: ^0.7.4
  json_serializable: ^6.5.3
  page_transition: ^2.1.0
  plugin_platform_interface: ^2.1.3
  # rxdart: ^0.28.0
  shared_preferences: ^2.3.2
  # sign_in_with_apple: ^6.1.2
  sign_in_with_apple: ^6.1.3
  dart_jsonwebtoken: ^2.14.1
  # sign_in_with_apple_platform_interface: ^1.1.0
  # sign_in_with_apple_web: ^2.1.0
  timeago: ^3.3.0
  url_launcher: ^6.1.6
  flutter_dialogs: ^3.0.0
#  flutter_easyloading: ^3.0.5
  flutter_html: ^3.0.0
  easy_debounce: ^2.0.2+1
  pinput: ^5.0.0
  loadmore: ^2.0.1
  #webviewx: 0.2.1
  carousel_slider: ^5.0.0
  image_picker: ^1.1.2
  flutter_inappwebview: ^6.1.1
#  flutter_facebook_keyhash:
#    git:
#      url: https://github.com/Rattasat1998/flutter_facebook_keyhash.git
#      ref: master
  #firebase_crashlytics: ^4.1.2
  flutter_bloc: ^8.1.6
  cached_network_image: ^3.2.3
  # leak_detector: ^1.0.1+4
  # flutter_native_image: ^0.0.6+1
  flutter_math_fork: ^0.7.2
  shimmer: ^3.0.0
  coupon_uikit: ^0.2.0
  scrollable_positioned_list: ^0.3.8
  pull_to_refresh: ^2.0.0
  share_plus: ^10.1.1
  image_gallery_saver_plus: ^4.0.1
  video_player: ^2.9.1
  stacked: ^3.4.1
  get_it: ^7.6.0
  local_auth: ^2.1.6
  flutter_secure_storage: ^9.0.0
#  flutter_vibrate: ^1.3.0
  #flutter_slidable: ^3.0.0
#  fluttertoast: ^8.2.2
  reorderable_grid_view: ^2.2.6
  provider: ^6.0.5
  flutter_launcher_icons: ^0.14.1
  #
  sliding_up_panel: ^2.0.0+1
  photo_view: ^0.15.0
  lottie: ^3.1.2
  otp_text_field: ^1.1.3
#  wakelock: ^0.4.0
  animated_text_kit: ^4.2.2
  permission_handler: ^11.0.0
  app_tracking_transparency: ^2.0.4
  flutter_local_notifications: ^17.2.3
  path_provider: ^2.1.2
  # firebase_messaging: ^15.1.2
  firebase_messaging: ^15.2.5
  socket_io_client: ^2.0.3+1
  scroll_to_index: ^3.0.1
  diffutil_dart: ^4.0.0
  visibility_detector: ^0.4.0+2
  flutter_parsed_text: ^2.2.1
  flutter_linkify: ^6.0.0
  bloc: ^8.1.4
  flutter_widget_from_html: ^0.16.0
  # flutter_native_splash: ^2.3.6
  flutter_staggered_grid_view: ^0.7.0
  flutter_dotenv: ^5.1.0
  flutter_slidable: ^3.0.1
  firebase_remote_config: ^5.1.2
  isar: ^3.1.0+1
  isar_flutter_libs: ^3.1.0+1
  dropdown_button2: ^2.3.9
  vertical_scroll_tabbar: ^0.0.4+4
#  uni_links: ^0.5.1
  # smart_auth: ^2.0.0
  smart_auth: ^3.2.0
  win32: ^5.5.4
  archive: ^3.6.1
  #app_links: ^6.3.2
  #firebase_dynamic_links: ^6.0.7
  # เลิกใช้ firebase_dynamic_links
  # firebase_dynamic_links:
  #   git:
  #     url: https://github.com/Rattasat1998/flutterfire.git
  #     ref: main
  #     path: packages/firebase_dynamic_links/firebase_dynamic_links
  animated_read_more_text: ^0.0.6
  font_awesome_flutter: ^10.7.0
  loading_indicator: ^3.1.1
  smooth_page_indicator: ^1.2.0+3
  gap: ^3.0.1
  crypto: ^3.0.2
  flick_video_player: ^0.9.0
  slide_countdown: ^2.0.3-dev.1
  iconify_flutter: ^0.0.7
  thaiqr: ^1.1.1
  awesome_bottom_bar: ^1.2.4
  modal_bottom_sheet: ^3.0.0
  easy_stepper: ^0.8.5+1
  easy_refresh: ^3.4.0
  flutter_cache_manager: ^3.4.1
  flutter_map: ^7.0.2
  latlong2: ^0.9.1
  geolocator: ^12.0.0
  google_maps_flutter: ^2.10.1
  geocoding: ^3.0.0
  pull_down_button: ^0.10.2
  mime: ^2.0.0
  shadcn_flutter: ^0.0.32
  flutter_hooks: ^0.21.2
  vertical_scrollable_tabview: ^0.1.1
  dismissible_page: ^1.0.2
  loading_animation_widget: ^1.3.0
  flutter_phoenix: ^1.1.1
  collection: ^1.19.1
  barcode: ^2.2.9
  barcode_widget: ^2.0.4
  app_links: ^6.4.0
  syncfusion_flutter_charts: ^29.2.5
  table_calendar: ^3.1.3
  translator_plus: ^1.0.1
  auto_animated: ^3.2.0
  image: ^4.1.3
  audioplayers: ^6.4.0
  webview_flutter: ^4.13.0
  html: ^0.15.6
  firebase_analytics: ^11.5.0
  package_info_plus: ^8.3.0
  device_info_plus: ^11.5.0
  connectivity_plus: ^6.1.4
  carrier_info: 
    git:
      url: https://github.com/Chueawangkham/carrier_info_fix.git
      ref: main
  new_version_plus: ^0.1.0
  chewie: ^1.12.1

dev_dependencies:
  build_runner: ^2.4.8
  built_value_generator: ^8.4.1

  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  isar_generator: ^3.1.0+1
  custom_lint: ^0.6.0



flutter_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path_ios: "assets/images/app_launcher_icon.png"
  image_path_android: "assets/images/app_launcher_icon.png"
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/images/app_launcher_icon.png"
  custom_scheme: "shopchill"

flutter:

  uses-material-design: true

  assets:
    - assets/fonts/sarabun/
    - assets/images/
    - assets/videos/
    - assets/audios/
    - assets/lottie_animations/
    - assets/rive_animations/
    - assets/pdfs/
    - assets/icons/
    - assets/banks/
    - assets/thai_qr/
    - assets/json/
    - assets/payment/
    - .env
    - assets/sounds/
    

  # fonts:
  #     - family: 'Sarabun'
  #       fonts:
  #         - asset: assets/fonts/Sarabun_Thin.ttf
  #           weight: 100
  #         - asset: assets/fonts/Sarabun_Light.ttf
  #           weight: 300
  #         - asset: assets/fonts/Sarabun_Text.ttf
  #         - asset: assets/fonts/Sarabun_Medium.ttf
  #           weight: 500
  #         - asset: assets/fonts/Sarabun_SemiBold.ttf
  #           weight: 600
  #         - asset: assets/fonts/sukhumvitset_bold.ttf
  #           weight: 700

  fonts:
    - family: 'Sarabun'
      fonts:
        - asset: assets/fonts/sarabun/Sarabun-Thin.ttf
          weight: 100
        - asset: assets/fonts/sarabun/Sarabun-Light.ttf
          weight: 100
        - asset: assets/fonts/sarabun/Sarabun-Regular.ttf
          weight: 100
        - asset: assets/fonts/sarabun/Sarabun-Medium.ttf
          weight: 100
        - asset: assets/fonts/sarabun/Sarabun-SemiBold.ttf
          weight: 100
        - asset: assets/fonts/sarabun/Sarabun-Bold.ttf
          weight: 100
        - asset: assets/fonts/sarabun/Sarabun-ExtraBold.ttf
          weight: 100
      


# flutter_native_splash:
  # color: "#0056d6"
#   background_image: "assets/images/0_SplashScreen.png"
# android: false
# android_12:
#     image: "assets/images/Logo_ShopChill_3_n.png"
# color: "#0056d6"
#     icon_background_color: "#ffffff"
#   ios: true
#   ios_content_mode: center
#   fullscreen: true
#   android_gravity: clip_horizontal


