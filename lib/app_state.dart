import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_util.dart';

import 'package:shop_chill_app/screens/product_page/model/product_model.dart';

class FFAppState {
  static final FFAppState _instance = FFAppState._internal();

  factory FFAppState() {
    return _instance;
  }

  FFAppState._internal() {
    initializePersistedState();
  }

  Future initializePersistedState() async {
    prefs = await SharedPreferences.getInstance();
    _token = prefs.getString('ff_token') ?? _token;
    _faceToken = prefs.getString('ff_face_token') ?? _faceToken;
    _uid = prefs.getString('ff_uid') ?? _uid;
    _profileId = prefs.getString('ff_profileId') ?? _profileId;
    _googleAccessToken = prefs.getString('ff_googleAccessToken') ?? _googleAccessToken;
    _oauthToken = prefs.getString('ff_oauthToken') ?? _oauthToken;
    _searchHistory = prefs.getStringList('ff_searchHistory') ?? _searchHistory;
    _saveFavorites =
        prefs.getStringList('ff_saveFavorites')?.map((x) {
          try {
            return jsonDecode(x);
          } catch (e) {
            print("Can't decode persisted json. Error: $e.");
            return {};
          }
        }).toList() ??
        _saveFavorites;
  }

  late SharedPreferences prefs;

  String _uid = '';
  String get uid => _uid;
  set uid(String value) {
    _uid = value;
    prefs.setString('ff_uid', value);
  }

  String _profileId = '';
  String get profileId => _profileId;
  set profileId(String value) {
    _profileId = value;
    prefs.setString('ff_profileId', value);
  }

  String _oauthToken = '';
  String get oauthToken => _oauthToken;
  set oauthToken(String value) {
    _oauthToken = value;
    prefs.setString('ff_oauthToken', value);
  }

  String _googleAccessToken = '';
  String get googleAccessToken => _googleAccessToken;
  set googleAccessToken(String value) {
    _googleAccessToken = value;
    prefs.setString('ff_googleAccessToken', value);
  }

  String _token = '';
  String get token => _token;

  set token(String value) {
    _token = value;
    prefs.setString('ff_token', value);
  }

  String _faceToken = '';
  String get faceToken => _faceToken;

  set faceToken(String value) {
    _faceToken = value;
    prefs.setString('ff_face_token', value);
  }
  /*

  set token(String _value) {
    _token = _value;
    prefs.setString('ff_token', _value);
  }
*/

  List<String> _searchHistory = [];
  List<String> get searchHistory => _searchHistory;
  set searchHistory(List<String> value) {
    _searchHistory = value;
    prefs.setStringList('ff_searchHistory', value);
  }

  void addToSearchHistory(String value) {
    _searchHistory.add(value);
    prefs.setStringList('ff_searchHistory', _searchHistory);
  }

  void removeFromSearchHistory(String value) {
    _searchHistory.remove(value);
    prefs.setStringList('ff_searchHistory', _searchHistory);
  }

  List _saveFavorites = [];
  List<Products> get saveFavorites => _saveFavorites.map((x) {
    try {
      return Products.fromJson(x);
    } catch (e) {
      print("Can't decode persisted json. Error: $e.");
      return Products(coupons: []);
    }
  }).toList();
  set saveFavorites(List<Products> value) {
    _saveFavorites = value;
    prefs.setStringList('ff_saveFavorites', value.map((x) => jsonEncode(x)).toList());
  }

  void addToSaveFavorites(dynamic value) {
    _saveFavorites.add(value);
    prefs.setStringList('ff_saveFavorites', _saveFavorites.map((x) => jsonEncode(x)).toList());
  }

  void removeFromSaveFavorites(dynamic value) {
    _saveFavorites.remove(value);
    prefs.setStringList('ff_saveFavorites', _saveFavorites.map((x) => jsonEncode(x)).toList());
  }
}
