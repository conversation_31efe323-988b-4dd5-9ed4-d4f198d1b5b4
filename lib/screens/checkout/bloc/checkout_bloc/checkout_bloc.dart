import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop_chill_app/app_routers.dart';
import 'package:shop_chill_app/app_state.dart';
import 'package:shop_chill_app/backend/api_requests/domain.dart';
import 'package:shop_chill_app/config/shopchill_loading/shopchill_loading.dart';
import 'package:shop_chill_app/screens/checkout/models/check_out_model.dart';
import 'package:shop_chill_app/screens/checkout/models/checkout_request.dart';
import 'package:shop_chill_app/screens/checkout/models/payment_model.dart';
import 'package:shop_chill_app/screens/checkout/repository/check_out_repository.dart';
import 'package:shop_chill_app/screens/checkout/widgets/paysolution_webview.dart';
import 'package:shop_chill_app/screens/my_cart/bloc/cart_bloc/cart_bloc.dart';
import 'package:shop_chill_app/screens/my_cart/models/cart_model.dart';
import 'package:shop_chill_app/screens/my_cart/repository/my_cart_repository.dart';
import 'package:shop_chill_app/screens/myorder/my_order_page.dart';
import 'package:shop_chill_app/screens/nav_bar/nav_bar_page.dart';
import 'package:shop_chill_app/screens/order/bloc/order_bloc.dart';
import 'package:shop_chill_app/screens/payment/payment_cubit/payment_cubit.dart';
import 'package:shop_chill_app/screens/payment/payment_screen.dart';
import 'package:shop_chill_app/screens/product_page/model/active_payment_gateways.dart';
import 'package:shop_chill_app/screens/product_page/model/calculate_price_model.dart';
import 'package:shop_chill_app/screens/product_page/model/product_model.dart';
import 'package:shop_chill_app/shered/enum/payment_gatway_type.dart';
import 'package:shop_chill_app/shered/enum/payment_provider.dart';
import 'package:shop_chill_app/shered/util/bank_store_redirect_link.dart';
import 'package:shop_chill_app/shered/util/payment_gatway_type_utils.dart';
import 'package:url_launcher/url_launcher.dart';

part 'checkout_event.dart';

part 'checkout_state.dart';

class CheckoutBloc extends Bloc<CheckoutEvent, CheckoutState> {
  final CheckOutRepository _checkOutRepository = CheckOutRepository();
  final MyCartRepository _myCartRepository = MyCartRepository();

  bool invoiceRequired = false;

  CheckoutBloc() : super(const CheckoutState()) {
    on<SelectProductCheckoutEvent>(_onSelectProduct);
    on<SubmitCheckoutEvent>(_onSubmitCheckoutEvent);
    on<ApplyCouponSystemEvent>(_onApplyCoupon);
    // on<ApplyCouponShopEvent>(_onApplyCouponShop);
    on<ClearCheckoutEvent>(_onClearCheckout);
  }

  Future<void> _onClearCheckout(ClearCheckoutEvent event, Emitter<CheckoutState> emit) async {
    emit(state.copyWith(seletedSystemCoupon: '', confirmedShopCoupons: [''], selectedList: [], selectedProductList: []));
  }

  Future<void> _onApplyCoupon(ApplyCouponSystemEvent event, Emitter<CheckoutState> emit) async {
    final state = this.state;
    emit(state.copyWith(seletedSystemCoupon: event.coupon));

    add(SelectProductCheckoutEvent(selectedCartList: event.selectedCartList, selectedProductList: event.selectedProductList, confirmedShopCoupons: event.confirmedShopCoupons));
  }

  // Future<void> _onApplyCouponShop(ApplyCouponShopEvent event, Emitter<CheckoutState> emit) async {
  //   // emit(state.copyWith(selectedCoupon: event.coupons.first));

  //   add(SelectProductCheckoutEvent(
  //     selectedCartList: event.selectedCartList,
  //     selectedProductList: event.selectedProductList,
  //   ));
  // }

  FutureOr<void> _onSelectProduct(SelectProductCheckoutEvent event, Emitter<CheckoutState> emit) async {
    final state = this.state;
    try {
      emit(state.copyWith(status: CheckoutStatus.initial));

      // 🔰 กรอง product ซ้ำออก
      final uniqueSelectedProducts = {for (var product in event.selectedProductList) product.id: product}.values.toList();

      final List<int> productIds = uniqueSelectedProducts.map((e) => int.parse(e.id.toString())).toList();

      if (productIds.isEmpty) {
        ShopChillLoading.showError("กรุณาเลือกสินค้า");
        emit(state.copyWith(priceModel: null, status: CheckoutStatus.failure));
        return;
      }

      final response = await _myCartRepository.calculatePrice(carts: productIds, shopCoupons: event.confirmedShopCoupons, coupon: state.seletedSystemCoupon ?? '');

      if (response != null && response['success'] == true) {
        print('on calculate success!');
        final cal = CalculatePriceModel.fromJson(response);
        emit(
          state.copyWith(
            status: CheckoutStatus.success,
            selectedList: event.selectedCartList,
            selectedProductList: uniqueSelectedProducts,
            priceModel: cal,
            confirmedShopCoupons: event.confirmedShopCoupons,
          ),
        );
      } else {
        ShopChillLoading.showError(response['message'] ?? "เกิดข้อผิดพลาด");
        emit(state.copyWith(status: CheckoutStatus.initial));

        final fallbackResponse = await _myCartRepository.calculatePrice(carts: productIds, shopCoupons: [], coupon: '');

        if (fallbackResponse != null && fallbackResponse['success'] == true) {
          print('on calculate success!');
          final fallbackCal = CalculatePriceModel.fromJson(fallbackResponse);

          emit(
            state.copyWith(
              status: CheckoutStatus.success,
              selectedList: event.selectedCartList,
              selectedProductList: uniqueSelectedProducts,
              priceModel: fallbackCal,
              confirmedShopCoupons: [''],
              seletedSystemCoupon: '',
            ),
          );
        } else {
          print('on calculate failure!');
          ShopChillLoading.showError("เกิดข้อผิดพลาด");
          emit(state.copyWith(priceModel: null, status: CheckoutStatus.failure));
        }
      }
    } catch (e) {
      ShopChillLoading.showError("เกิดข้อผิดพลาด \n$e");

      emit(state.copyWith(priceModel: null, status: CheckoutStatus.failure));
    }
  }

  Future<void> _onSubmitCheckoutEvent(SubmitCheckoutEvent event, Emitter<CheckoutState> emit) async {
    try {
      ShopChillLoading.show(status: 'กำลังเตรียมสั่งซื้อ');

      final checkout = await _checkOutRepository.checkout(checkoutRequest: event.checkoutRequest);

      if (checkout.success == true) {
        print(event.selectedPayment?.provider);
        await _handlePaymentFlow(event, checkout, emit);
      } else {
        print('checkout error: ${checkout.message}');
        // ShopChillLoading.dismiss();
        ShopChillLoading.showInfo(checkout.message ?? "ไม่สามารถดำเนินการสั่งซื้อได้");
        emit(state.copyWith(checkOutModel: checkout, status: CheckoutStatus.failure));
      }
      event.context.read<CartBloc>().add(GetCartEvent());
    } catch (e, stackTrace) {
      print('Checkout error: $e');
      print(stackTrace);
      ShopChillLoading.dismiss();
      emit(state.copyWith(status: CheckoutStatus.failure));
    } finally {
      await Future.delayed(const Duration(milliseconds: 2000));
      ShopChillLoading.dismiss();
    }
  }

  Future<void> _handlePaymentFlow(SubmitCheckoutEvent event, CheckOutModel checkout, Emitter<CheckoutState> emit) async {
    try {
      print('checkout data code: ${event.selectedPayment?.code}');
      final PaymentGatewayType paymentType = PaymentGatewayUtils.fromCode(event.selectedPayment?.code);
      debugPrint("paymentType : $paymentType");

      switch (paymentType) {
        case PaymentGatewayType.ibanking:
          await _handleIBankingPayment(event, checkout);
          break;

        case PaymentGatewayType.creditCard:
          await _handleCreditCardPayment(event, checkout);
          break;

        case PaymentGatewayType.cod:
        case PaymentGatewayType.qrcode:
        case PaymentGatewayType.transfer:
        case PaymentGatewayType.unknown:
          await _handleOtherPayments(paymentType, event, checkout, emit);
          break;
      }
    } catch (e) {
      await Future.delayed(const Duration(milliseconds: 2000));

      ShopChillLoading.dismiss();
      debugPrint('Payment Flow error: $e');
      emit(state.copyWith(status: CheckoutStatus.failure));
    }
  }

  //ชำระผ่านแอพพลิเคชั่นธนาคาร
  Future<void> _handleIBankingPayment(SubmitCheckoutEvent event, CheckOutModel checkout) async {
    try {
      final response = await _checkOutRepository.paymentMobileBanking(paymentId: checkout.data?.paymentId ?? 0);

      if (response['status'] == true) {
        ShopChillLoading.dismiss();

        final String redirectUrl = response['data']['redirect_url'] ?? '';
        final uri = Uri.tryParse(redirectUrl);
        try {
          await launchUrl(uri!);
          _navigateToMyOrder(event.context);
          return;
        } catch (e) {
          // ถ้าเปิดไม่ได้ fallback ไปยัง Store
          final fallbackUrl = BankStoreRedirectLink.getBankStoreRedirectLink(redirectUrl);
          final fallbackUri = Uri.tryParse(fallbackUrl);

          try {
            await launchUrl(fallbackUri!);
            _navigateToMyOrder(event.context);
            return;
          } catch (e) {
            _navigateToMyOrder(event.context);
            ShopChillLoading.showError("ไม่สามารถดำเนินการ Mobile Banking ได้");
            return;
          }
        }
      } else {
        ShopChillLoading.showError(response['message'] ?? "ไม่สามารถดำเนินการผ่าน Mobile Banking ได้");
        _navigateToMyOrder(event.context);
      }
    } catch (e, t) {
      debugPrintStack(label: "err : $e", stackTrace: t);
    } finally {
      await Future.delayed(const Duration(milliseconds: 2000));
      ShopChillLoading.dismiss();
    }
  }

  void _navigateToMyOrder(BuildContext context) {
    context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: ''));
    Navigator.pushReplacement(context, CupertinoPageRoute(builder: (_) => const MyOrderPage()));
  }
  /*  Future<void> _handleIBankingPayment(SubmitCheckoutEvent event, CheckOutModel checkout) async {
    try {
      final response = await _checkOutRepository.paymentMobileBanking(paymentId: checkout.data?.paymentId ?? 0);

      if (response['status'] == true) {
        ShopChillLoading.dismiss();

        final String redirectUrl = response['data']['redirect_url'] ?? '';

        final canLaunch = await canLaunchUrl(Uri.parse(redirectUrl));

        if (canLaunch) {
          await launchUrl(Uri.parse(redirectUrl));
        } else {
          final String appStoreUrl = BankStoreRedirectLink.getBankStoreRedirectLink(redirectUrl);
          await launchUrl(Uri.parse(appStoreUrl));
        }

        event.context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: ''));
        // Navigator.push(event.context, MaterialPageRoute(builder: (context) => const MyOrderPage()));
        Navigator.pushReplacement(event.context, CupertinoPageRoute(builder: (context) => const MyOrderPage()));
      } else {
        ShopChillLoading.showError(response['message'] ?? "ไม่สามารถดำเนินการ Mobile Banking ได้");
        event.context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: ''));
        // Navigator.push(event.context, MaterialPageRoute(builder: (context) => const MyOrderPage()));
        Navigator.pushReplacement(event.context, CupertinoPageRoute(builder: (context) => const MyOrderPage()));
      }
    } catch (e, t) {
      debugPrintStack(label: "err : $e", stackTrace: t);
    } finally {
      ShopChillLoading.dismiss();
    }
  } */

  //ชำระผ่านบัตรเครดิต
  Future<void> _handleCreditCardPayment(SubmitCheckoutEvent event, CheckOutModel checkout) async {
    try {
      if (event.selectedPayment?.provider == PaymentProvider.flashpay.code) {
        _flashPayCardPayment(checkout, event);
      } else if (event.selectedPayment?.provider == PaymentProvider.paysolution.code) {
        _paysolutionCardPayment(checkout, event);
      }
    } catch (e) {
      debugPrintStack(label: "err : $e");
    }
  }

  Future<void> _paysolutionCardPayment(CheckOutModel checkout, SubmitCheckoutEvent event) async {
    try {
      final getPayment = await _checkOutRepository.getpayment(paymentid: checkout.data?.paymentId ?? 0);
      final masterOrderId = '${getPayment.data?.orders.first.masterOrderId ?? ''}';

      ShopChillLoading.dismiss();
      final String url = '${domain.domain}/api/payment/pay/credit-card?payment_id=$masterOrderId&api_token=${FFAppState().token}';
      Navigator.push(
        event.context,
        CupertinoPageRoute(
          builder: (context) {
            return PaysolutionWebview(url: url);
          },
        ),
      );
      // await launchUrl(Uri.parse(creditPayment.data?.redirectUrl ?? ''));
      // event.context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: ''));
      // // Navigator.push(event.context, MaterialPageRoute(builder: (context) => const MyOrderPage()));

      // Navigator.pushReplacement(event.context, CupertinoPageRoute(builder: (context) => const MyOrderPage()));
    } catch (e, t) {
      debugPrintStack(label: "err : $e", stackTrace: t);
    } finally {
      await Future.delayed(const Duration(milliseconds: 2000));
      ShopChillLoading.dismiss();
    }
  }

  Future<void> _flashPayCardPayment(CheckOutModel checkout, SubmitCheckoutEvent event) async {
    try {
      final getPayment = await _checkOutRepository.getpayment(paymentid: checkout.data?.paymentId ?? 0);
      final masterOrderId = '${getPayment.data?.orders.first.masterOrderId ?? ''}';
      final creditPayment = await _checkOutRepository.getCreditModel(paymentId: masterOrderId);

      ShopChillLoading.dismiss();
      await launchUrl(Uri.parse(creditPayment.data?.redirectUrl ?? ''));
      event.context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: ''));
      // Navigator.push(event.context, MaterialPageRoute(builder: (context) => const MyOrderPage()));

      Navigator.pushReplacement(event.context, CupertinoPageRoute(builder: (context) => const MyOrderPage()));
    } catch (e, t) {
      debugPrintStack(label: "err : $e", stackTrace: t);
    } finally {
      await Future.delayed(const Duration(milliseconds: 2000));
      ShopChillLoading.dismiss();
    }
  }

  //ชำระเงินในระบบ [Qr code,โอนเงิน,เก็บเงินปลายทาง]
  Future<void> _handleOtherPayments(PaymentGatewayType paymentType, SubmitCheckoutEvent event, CheckOutModel checkout, Emitter<CheckoutState> emit) async {
    final GetPaymentModel getpayment = await _checkOutRepository.getpayment(paymentid: int.parse((checkout.data?.paymentId ?? 0).toString()));

    if (getpayment.status == false) {
      ShopChillLoading.showError(getpayment.msg ?? 'บางอย่างผิดพลาด');
      emit(state.copyWith(status: CheckoutStatus.failure));
    } else {
      // ตรวจสอบช่องทางการชำระเงินเพิ่มเติม
      final order = getpayment.data?.orders.first;
      final payment = getpayment.data?.payment;

      if (order == null || payment == null) {
        ShopChillLoading.showError('บางอย่างผิดพลาด ไม่สามารถโหลดข้อมูลได้');
        emit(state.copyWith(status: CheckoutStatus.failure));
        return;
      }

      event.context.read<PaymentCubit>().onCheckPayment(
        paymentType: paymentType,
        masterOrderId: order.masterOrderId.toString(),
        orderNo: order.code.toString(),
        paymentId: payment.id.toString(),
        gatewayId: payment.gatewayId.toString(),
        provider: null,
        context: event.context,
      );

      await event.context.read<PaymentCubit>().stream.first;

      final paymentState = event.context.read<PaymentCubit>().state;

      if (paymentState is PaymentLoadErrorState) {
        emit(state.copyWith(status: CheckoutStatus.success, checkOutModel: checkout));
        ShopChillLoading.showError(paymentState.message);
        Navigator.pushReplacement(event.context, CupertinoPageRoute(builder: (context) => const NavBarPage(initialPage: 0)));
        return;
      } else {
        ShopChillLoading.dismiss();
        emit(state.copyWith(status: CheckoutStatus.success, checkOutModel: checkout));
        Navigator.pushAndRemoveUntil(
          event.context,
          CupertinoPageRoute(
            builder: (context) => const PaymentScreen(goToRouterName: AppRoutes.myOrder, orderId: '', selectIndex: '0'),
          ),
          (route) {
            print("route.settings.name ${route.settings.name}");
            return route.settings.name == AppRoutes.navBar || route.settings.name == AppRoutes.home;
          },
        );
        // Navigator.pushReplacement(
        //   event.context,
        //   CupertinoPageRoute(
        //     builder: (context) => const PaymentScreen(goToRouterName: AppRoutes.myOrder, orderId: '', selectIndex: '0'),
        //   ),
        // );
      }
    }
  }

  // Apply Coupon Logic
  // void _onApplyCoupon(ApplyCouponEvent event, Emitter<CheckoutState> emit) {
  //   emit(state.copyWith(selectedCoupon: event.coupon));
  //   add(CalculateCheckoutTotalEvent());
  // }

  /*  Future<void> _onSubmitCheckoutEvent(SubmitCheckoutEvent event, Emitter<CheckoutState> emit) async {
    try {
      // emit(state.copyWith(status: CheckoutStatus.initial));
      // ShopChillLoading.show(status: 'กำลังเตียมคำสั่งซื้อ');
      ShopChillLoading.show(status: 'กำลังเตรียมสั่งซื้อ');
      print('state.status = ${state.status}');

      // if (state.status == CheckoutStatus.initial) {
      final checkout = await _checkOutRepository.checkout(
        cartIds: event.cartIds,
        addressId: event.addressId,
        gatewayId: event.gatewayId,
        shippingFee: event.shippingFee,
        discountOrders: event.discountOrders,
        discountShipping: event.discountShipping,
        coupon: event.coupon,
        shopCoupons: event.shopCoupons.isEmpty ? [] : [event.shopCoupons],
        invoiceRequired: invoiceRequired ? 1 : 0,
        invoiceTaxId: event.invoiceTaxId,
        invoiceBranch: event.invoiceBranch,
        invoiceName: invoiceRequired ? event.invoiceName : '',
        invoiceAddress: invoiceRequired ? event.invoiceAddress : '',
        invoicePhone: invoiceRequired ? event.invoicePhone : '',
        invoiceDistrict: invoiceRequired ? event.invoiceDistrict : '',
        invoiceAmphure: invoiceRequired ? event.invoiceAmphure : '',
        invoiceProvince: invoiceRequired ? event.invoiceProvince : '',
        invoiceZipcode: invoiceRequired ? event.invoiceZipcode : '',
        ibankingBankName: event.payment.bankCode ?? '',
      );

      print('checkout = ${checkout.toJson()}');

      if (checkout.success!) {
        try {
          if (event.payment.id == 6) {
            final response = await _checkOutRepository.paymentMobileBanking(paymentId: int.parse((checkout.data?.paymentId ?? 0).toString()));
            print('data for direct to bank = $response');

            if (response['status'] == true) {
              ShopChillLoading.dismiss();
              //emit(state.copyWith(status: CheckoutStatus.success, checkOutModel: checkout));
              //print('redirect_url = ${response['data']['redirect_url']}');
              final String customUrl = response['data']['redirect_url'];
              // await PaymentURLLauncher.launchCustomURL(customUrl);
              await launchUrl(Uri.parse(customUrl), mode: LaunchMode.externalApplication);
              event.context.read<OrderBloc>().add(GetOrderEvent(limit: '20', offset: '0', status: ''));
              Navigator.push(event.context, MaterialPageRoute(builder: (context) => const MyOrderPage()));
              return;
            } else {
              ShopChillLoading.showError(response['message']);
              //emit(state.copyWith(status: CheckoutStatus.failure));
            }

            ShopChillLoading.dismiss();
            return;
          }

          if (event.payment.id == 5) {
            print('pay with credit card');
            final GetPaymentModel getpayment = await _checkOutRepository.getpayment(paymentid: int.parse((checkout.data?.paymentId ?? 0).toString()));

            final String masterOrderId = getpayment.data!.orders.first.masterOrderId.toString();

            final PaymentCreditModel payment = await CheckOutRepository().getCreditModel(paymentId: masterOrderId);
            await launchUrl(Uri.parse(payment.checkoutUrl!));
            ShopChillLoading.dismiss();
            event.context.read<OrderBloc>().add(GetOrderEvent(limit: '20', offset: '0', status: ''));
            Navigator.push(event.context, MaterialPageRoute(builder: (context) => const MyOrderPage()));
            return;
          }

          final GetPaymentModel getpayment = await _checkOutRepository.getpayment(paymentid: int.parse((checkout.data?.paymentId ?? 0).toString()));
          print('getpayment = ${getpayment.status}');
          print('getpayment = ${getpayment.msg}');

          if (getpayment.status == false) {
            ShopChillLoading.showError(getpayment.msg ?? 'บางอย่างผิดพลาด');
            emit(state.copyWith(status: CheckoutStatus.failure));
          } else {
            // event.context.read<CartBloc>().add(GetCartEvent());
            // ตรวจสอบช่องทางการชำระเงิน
            event.context.read<PaymentCubit>().onCheckPayment(
                  masterorderId: getpayment.data!.orders.first.masterOrderId.toString(),
                  gatewayId: getpayment.data!.payment!.gatewayId.toString(),
                  orderNo: getpayment.data!.orders.first.code.toString(),
                );

            await event.context.read<PaymentCubit>().stream.first;

            final payment = event.context.read<PaymentCubit>().state;

            if (payment is PaymentLoadErrorState) {
              emit(state.copyWith(status: CheckoutStatus.success, checkOutModel: checkout));
              ShopChillLoading.showError(payment.message);
              Navigator.pushReplacement(
                event.context,
                CupertinoPageRoute(
                  builder: (context) => const NavBarPage(),
                ),
              );
              return;
            } else {
              ShopChillLoading.dismiss();
              emit(state.copyWith(status: CheckoutStatus.success, checkOutModel: checkout));
              Navigator.pushReplacement(
                event.context,
                CupertinoPageRoute(
                  builder: (context) => const PaymentScreen(),
                ),
              );
            }
          }
        } on Exception catch (e) {
          print('exception = $e');
          emit(state.copyWith(status: CheckoutStatus.failure));
        }
      } else {
        emit(state.copyWith(checkOutModel: checkout, status: CheckoutStatus.success));
        ShopChillLoading.dismiss();
        ShopChillLoading.showError(checkout.message!);
      }
    } catch (e, stackTrace) {
      print('check out submit exception = $e');
      print(stackTrace);
      emit(state.copyWith(status: CheckoutStatus.failure));
    }
  } */
}
