import 'dart:io';
import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:image_picker/image_picker.dart';
import 'package:lottie/lottie.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shop_chill_app/app_routers.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/screens/checkout/repository/check_out_repository.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_theme.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_util.dart';
import 'package:shop_chill_app/screens/my_cart/bloc/cart_bloc/cart_bloc.dart';
import 'package:shop_chill_app/screens/my_cart/widgets/cart_promotion_text_row_widget.dart';
import 'package:shop_chill_app/screens/myorder/my_order_page.dart';
import 'package:shop_chill_app/screens/nav_bar/nav_bar_page.dart';
import 'package:shop_chill_app/screens/order/bloc/order_bloc.dart';
import 'package:shop_chill_app/screens/payment/payment_cubit/payment_cubit.dart';
import 'package:shop_chill_app/screens/payment/payment_oncheck_cubit/payment_oncheck_cubit.dart';
import 'package:shop_chill_app/screens/product_page/model/product_model.dart';
import 'package:shop_chill_app/screens/video/widgets/custom_image_network.dart';
import 'package:shop_chill_app/shered/assets/image_assets.dart';
import 'package:shop_chill_app/shered/util/custom_text.dart';
import 'package:shop_chill_app/shered/util/extensions/gap_extension.dart';
import 'package:shop_chill_app/shered/util/format_number.dart';
import 'package:shop_chill_app/shered/util/format_numbers.dart';
import 'package:shop_chill_app/shered/util/payment_gatway_type_utils.dart';
import 'package:shop_chill_app/shered/widgets/dialog/custom_alert_dialog.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../config/shopchill_loading/shopchill_loading.dart';
import '../../shered/util/bank_store_redirect_link.dart';
import '../paymentQrCode/qrcode_with_logo_widget.dart';

class PaymentScreen extends StatefulWidget {
  final String txn;
  final String masterOrderId;
  final String paymentId;
  final String gatewayId;
  final String pid;
  final String goToRouterName;
  final String orderId;
  final String selectIndex;

  const PaymentScreen({
    super.key,
    this.txn = '',
    this.masterOrderId = '',
    this.paymentId = '',
    this.gatewayId = '',
    this.pid = '',
    required this.goToRouterName,
    required this.orderId,
    required this.selectIndex,
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  static GlobalKey previewContainer = GlobalKey();
  final formKey = GlobalKey<FormState>();
  File? file;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PaymentCubit, PaymentState>(
      builder: (context, state) {
        print('state = $state');
        if (state is PaymentQrState) {
          if (state.paymentqr.expriedTime != null) {
            context.read<PaymentCubit>().startCountdown(state.paymentqr.expriedTime!);
          }

          return WillPopScope(
            onWillPop: () async {
              final shouldLeave = await _showBackDialog(context);
              return shouldLeave ?? false;
            },
            child: Scaffold(appBar: _buildAppBarPaymentQr(context), body: _buildBodyPaymentQr(state, context)),
          );
        } else if (state is PaymentPurchaseState) {
          final bool iBanking = state.paymentpurchase.data!.paymentName == 'iBanking';
          final String title = state.paymentpurchase.data!.paymentName == 'iBanking'
              ? 'โมบายแบงค์กิ้ง'
              : state.paymentpurchase.data!.paymentName == 'TrueMoney'
              ? 'True Money Wallet'
              : 'รอการชำระเงิน';

          return WillPopScope(
            onWillPop: () async {
              final shouldLeave = await _showBackDialog(context);
              return shouldLeave ?? false;
            },
            child: GestureDetector(
              onTap: () => FocusScope.of(context).unfocus(),
              child: Form(
                key: formKey,
                child: Scaffold(
                  backgroundColor: ColorThemeConfig.primaryBackground,
                  appBar: _buildAppBarPaymentPurchase(title, context),
                  body: _buildBodyPaymentPurchase(state, iBanking, context),
                ),
              ),
            ),
          );
        } else if (state is PaymentTrueMoneyState) {
          return _buildPaymentTrueMoneySection();
        } else if (state is PaymentCodState) {
          return _buildPaymentCodSection();
        } else if (state is PaymentExpireState) {
          return Scaffold(
            appBar: AppBar(
              elevation: 0,
              backgroundColor: Colors.white,
              title: const CustomText(text: 'รายการชำระเงินหมดอายุ', fontWeight: FontWeight.bold, fontSize: 18),
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CustomText(text: 'รายการชำระเงินหมดอายุ', fontSize: 16),
                  Center(
                    child: CupertinoButton(
                      borderRadius: BorderRadius.circular(10),
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      color: ColorThemeConfig.primaryColor,
                      onPressed: () async {
                        print('masterOrderId: ${widget.masterOrderId}');
                        print('paymentId: ${widget.paymentId}');
                        print('txn: ${widget.txn}');

                        context.read<PaymentCubit>().reGenerateQr(
                          masterOrderId: int.parse(widget.masterOrderId.toString()),
                          paymentId: int.parse(widget.paymentId.toString()),
                          gatewayId: int.parse(widget.gatewayId.toString()),
                          txn: widget.txn,
                        );
                      },
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'ตรวจสอบอีกครั้ง',
                            style: FlutterFlowTheme.of(context).title3.copyWith(fontSize: 16, color: Colors.white, fontWeight: FontWeight.bold),
                          ),
                          const Icon(Icons.refresh, color: Colors.white),
                        ],
                      ),
                    ),
                    /*  CupertinoButton(
                      onPressed: () {},
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CustomText(
                            text: 'ตรวจสอบอีกครั้ง',
                            fontSize: 16,
                            color: Colors.blue,
                          ),
                          Icon(
                            Icons.refresh,
                            color: Colors.blue,
                          )
                        ],
                      ),
                    ), */
                  ),
                ],
              ),
            ),
          );
        } else if (state is PaymentLoading) {
          return Scaffold(
            appBar: AppBar(
              backgroundColor: Colors.white,
              elevation: 0,
              centerTitle: true,
              title: const CustomText(text: 'รอการชำระเงิน', fontSize: 18, fontWeight: FontWeight.bold),
              automaticallyImplyLeading: false,
              actions: [
                IconButton(
                  onPressed: () {
                    context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: ''));
                    context.read<CartBloc>().add(GetCartEvent());
                    Navigator.pushReplacement(context, CupertinoPageRoute(builder: (context) => const MyOrderPage()));
                  },
                  icon: const Icon(Icons.close, color: Colors.black),
                ),
              ],
            ),
            body: const Center(child: CircularProgressIndicator.adaptive()),
          );
        } else if (state is PaymentWaitingState) {
          return Scaffold(
            appBar: AppBar(
              flexibleSpace: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xFF0056D6), Color(0xFF0073E6), Color(0xFF0084F0)],
                    stops: [0, 1, 1],
                    begin: AlignmentDirectional(0, -1),
                    end: AlignmentDirectional(0, 1),
                  ),
                ),
              ),
              title: const Text(
                'รอการตรวจสอบการชำระเงิน',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 14),
              ),
            ),
          );
        } else if (state is PaymentCreditcardState) {
          return _buildPaymentCreditcardSection(context, state.provider);
        } else {
          return Scaffold(
            appBar: AppBar(
              flexibleSpace: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xFF0056D6), Color(0xFF0073E6), Color(0xFF0084F0)],
                    stops: [0, 1, 1],
                    begin: AlignmentDirectional(0, -1),
                    end: AlignmentDirectional(0, 1),
                  ),
                ),
              ),
              title: const Text('เกิดข้อผิดพลาดบางอย่าง'),
            ),
          );
        }
      },
    );
  }

  Widget _buildPaymentCreditcardSection(BuildContext context, String provider) {
    return WillPopScope(
      onWillPop: () async {
        final shouldLeave = await _showBackDialog(context);
        return shouldLeave ?? false;
      },
      child: Scaffold(
        appBar: AppBar(
          centerTitle: true,
          title: const CustomText(text: 'รอการชำระเงิน', fontSize: 18, fontWeight: FontWeight.bold),
          backgroundColor: Colors.white,
          automaticallyImplyLeading: false,
          elevation: 0,
          actions: [
            IconButton(
              onPressed: () {
                /* context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: ''));
                context.read<CartBloc>().add(GetCartEvent());
                Navigator.pushReplacement(context, CupertinoPageRoute(builder: (context) => const MyOrderPage()));*/
                /* if (widget.goToRouterName == AppRoutes.myOrder) {
                  context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: ''));
                  context.read<CartBloc>().add(GetCartEvent());
                  Navigator.pushReplacement(
                      context,
                      CupertinoPageRoute(
                          builder: (context) => MyOrderPage(
                                selectIndex: int.parse(widget.selectIndex.toString()),
                              )));
                } else {
                  context.read<OrderBloc>().add(GetOrderDetailEvent(widget.orderId));
                  Navigator.pop(context);
                } */
                _showBackDialog(context);
              },
              icon: const Icon(Icons.close, color: Colors.black),
            ),
            const SizedBox(width: 10),
          ],
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Lottie.asset(UImageAssets.WAITING_PAYMENT),
              Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.access_time, color: Colors.grey, size: 20),
                  5.gap,
                  Text('รอการดำเนินการชำระเงิน', style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700, fontSize: 16)),
                ],
              ),
              5.gap,
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 18),
                child: Text(
                  'การชำระเงินของคุณยังไม่เสร็จสมบูรณ์ กรุณาดำเนินการ\nชำระเงินอีกครั้งเพื่อยืนยันคำสั่งซื้อ',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Colors.grey, fontSize: 12),
                  textAlign: TextAlign.center,
                ),
              ),
              20.gap,
              CupertinoButton(
                borderRadius: BorderRadius.circular(10),
                padding: const EdgeInsets.symmetric(horizontal: 20),
                color: ColorThemeConfig.primaryColor,
                onPressed: () async {
                  print('masterOrderId: ${widget.masterOrderId}');
                  print('paymentId: ${widget.paymentId}');
                  print('txn: ${widget.txn}');

                  context.read<PaymentCubit>().onCheckPayment(
                    paymentType: PaymentGatewayUtils.fromId(int.parse(widget.paymentId.toString())),
                    paymentId: widget.paymentId.toString(),
                    masterOrderId: widget.masterOrderId.toString(),
                    gatewayId: widget.paymentId.toString(),
                    orderNo: widget.txn,
                    provider: provider,
                    context: context,
                  );
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'ลองอีกครั้ง',
                      style: FlutterFlowTheme.of(context).title3.copyWith(fontSize: 16, color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                    const Icon(Icons.refresh, color: Colors.white),
                  ],
                ),
              ),
              (MediaQuery.of(context).size.height / 6).gap,
            ],
          ),
        ),
      ),
    );
  }

  Scaffold _buildPaymentCodSection() {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        bottom: false,
        child: SingleChildScrollView(
          child: BlocBuilder<PaymentOncheckCubit, PaymentOncheckState>(
            builder: (context, state) {
              if (state is PaymentOncheckLoaded) {
                return Scrollbar(
                  thumbVisibility: true,
                  thickness: 4,
                  radius: const Radius.circular(60),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          // 'assets/images/bags.png',
                          UImageAssets.BAGS,
                          fit: BoxFit.cover,
                          scale: 2.5,
                        ),
                        const SizedBox(height: 20),
                        const Text('ขอบคุณสำหรับคำสั่งซื้อ!', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                        const SizedBox(height: 5),
                        const Padding(
                          padding: EdgeInsets.symmetric(horizontal: 32),
                          child: Text('คุณได้ทำการเลือกการชำระแบบปลายทาง กรุณารอการตรวจสอบจากร้านค้า', style: TextStyle(fontSize: 14), textAlign: TextAlign.center),
                        ),
                        const SizedBox(height: 20),
                        Container(
                          padding: const EdgeInsets.only(left: 10, right: 10, top: 10),
                          color: Colors.white,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('รายละเอียดคำสั่งซื้อ', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                              const SizedBox(height: 10),
                              ListView.builder(
                                shrinkWrap: true,
                                padding: const EdgeInsets.symmetric(horizontal: 10),
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: state.carts.length,
                                itemBuilder: (context, index) {
                                  final shop = state.carts[index];
                                  final List<Products> selectedProductList = state.selectedProductList.where((element) => element.shopId == shop.shopId).toList();
                                  return Column(
                                    children: [
                                      GestureDetector(
                                        child: Row(
                                          children: [
                                            CustomImageNetwork(url: shop.shopLogo.toString(), size: 25, borderRadius: BorderRadius.circular(6)),
                                            const SizedBox(width: 10),
                                            Text(shop.shopName.toString(), style: const TextStyle(fontWeight: FontWeight.bold)),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(height: 10),
                                      ListView.builder(
                                        itemCount: selectedProductList.length,
                                        shrinkWrap: true,
                                        physics: const NeverScrollableScrollPhysics(),
                                        itemBuilder: (context, indexP) {
                                          final product = selectedProductList[indexP];

                                          return Padding(
                                            padding: const EdgeInsets.only(bottom: 10),
                                            child: Column(
                                              children: [
                                                Row(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    CustomImageNetwork(borderRadius: BorderRadius.circular(8.0), url: product.thumbnail.toString(), size: 70),
                                                    const SizedBox(width: 10),
                                                    Expanded(
                                                      child: Column(
                                                        crossAxisAlignment: CrossAxisAlignment.start,
                                                        mainAxisAlignment: MainAxisAlignment.start,
                                                        children: [
                                                          Text(
                                                            product.name.toString(),
                                                            maxLines: 1,
                                                            overflow: TextOverflow.ellipsis,
                                                            style: Theme.of(
                                                              context,
                                                            ).textTheme.bodySmall?.copyWith(fontSize: 13, fontWeight: FontWeight.w600, color: Colors.black87),
                                                          ),
                                                          if (product.optionName.trim().isNotEmpty)
                                                            Text(
                                                              "${product.optionName} : ${product.optionValue}",
                                                              style: FlutterFlowTheme.of(
                                                                context,
                                                              ).bodyText1.copyWith(fontSize: 12, fontWeight: FontWeight.normal, color: Colors.grey),
                                                            ),
                                                          Row(
                                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            children: [
                                                              Row(
                                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                                children: [
                                                                  Text(
                                                                    "฿${product.discountPrice == 0 ? product.price.toString() : product.discountPrice.toString()} ",
                                                                    style: FlutterFlowTheme.of(context).bodyText1.override(
                                                                      fontFamily: 'Sarabun',
                                                                      color: const Color(0xFF0073E6),
                                                                      fontSize: 14,
                                                                      fontWeight: FontWeight.bold,
                                                                      useGoogleFonts: false,
                                                                    ),
                                                                  ),
                                                                  Text(
                                                                    product.discountPrice != product.price ? '฿${product.price.toString()}' : '',
                                                                    maxLines: 2,
                                                                    style: FlutterFlowTheme.of(context).bodyText1.override(
                                                                      fontFamily: 'Sarabun',
                                                                      color: const Color(0xFF91929D),
                                                                      fontSize: 14,
                                                                      fontWeight: FontWeight.w700,
                                                                      decoration: TextDecoration.lineThrough,
                                                                      useGoogleFonts: false,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                              Row(
                                                                children: [
                                                                  Text(
                                                                    'จำนวน : ',
                                                                    style: FlutterFlowTheme.of(context).bodyText1.copyWith(fontSize: 14, fontWeight: FontWeight.w700),
                                                                  ),
                                                                  Text(
                                                                    product.productQty.toString(),
                                                                    style: FlutterFlowTheme.of(context).bodyText1.copyWith(fontSize: 14, fontWeight: FontWeight.w700),
                                                                  ),
                                                                ],
                                                              ),
                                                            ],
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                indexP == selectedProductList.length - 1 ? const SizedBox() : const Divider(),
                                              ],
                                            ),
                                          );
                                        },
                                      ),
                                      index == state.carts.length - 1 ? const SizedBox() : const Divider(),
                                    ],
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        10.gap,
                        Container(
                          padding: const EdgeInsetsDirectional.fromSTEB(12, 12, 12, 8),
                          color: Colors.white,
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CartPromotionTextRowWidget(prefix: 'ราคาสินค้า', fontSize: 14, value: '฿${formatNumberWithDot(double.parse(state.checkoutPrice.toString()))}'),
                              CartPromotionTextRowWidget(prefix: 'ค่าจัดส่ง', fontSize: 14, value: '฿${formatNumberWithDot(double.parse(state.shippingPrice.toString()))}'),
                              CartPromotionTextRowWidget(
                                prefix: 'ส่วนลด',
                                fontSize: 14,
                                value: state.discount > 0 ? '-฿${formatNumberWithDot(double.parse(state.discount.toString()))}' : '฿0',
                                valueColor: state.discount > 0 ? Colors.red : Colors.grey,
                              ),
                              Container(padding: const EdgeInsets.symmetric(vertical: 12), alignment: Alignment.center, child: Image.asset(UImageAssets.LINE_CART)),
                              CartPromotionTextRowWidget(
                                prefix: 'ยอดรวมคำสั่งซื้อ',
                                value: '฿${formatNumberWithDot(double.parse(state.amoutPrice.toString()))}',
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ],
                          ),
                        ),
                        20.gap,
                        GestureDetector(
                          onTap: () async {
                            await Navigator.pushAndRemoveUntil(context, CupertinoPageRoute(builder: (context) => const NavBarPage(initialPage: 0)), (r) => false);
                          },
                          child: Container(
                            width: double.infinity,
                            height: 50,
                            margin: const EdgeInsets.symmetric(horizontal: 10),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(color: ColorThemeConfig.primaryColor, borderRadius: BorderRadius.circular(10)),
                            child: Text(
                              'ดูสินค้าต่อ',
                              textAlign: TextAlign.center,
                              style: FlutterFlowTheme.of(context).title2.copyWith(fontSize: 16, color: Colors.white),
                            ),
                          ),
                        ),
                        const SizedBox(height: 10),
                        GestureDetector(
                          onTap: () async {
                            context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: ''));
                            context.read<CartBloc>().add(GetCartEvent());
                            // Navigator.push(context, CupertinoPageRoute(builder: (context) => const MyOrderPage()));
                            Navigator.pushReplacement(context, CupertinoPageRoute(builder: (context) => const MyOrderPage()));
                          },
                          child: Container(
                            width: double.infinity,
                            margin: const EdgeInsets.symmetric(horizontal: 10),
                            height: 50,
                            decoration: BoxDecoration(color: Colors.grey.shade200, borderRadius: BorderRadius.circular(12)),
                            child: Align(
                              alignment: const AlignmentDirectional(0, 0),
                              child: Text(
                                'ไปหน้าออเดอร์',
                                textAlign: TextAlign.center,
                                style: FlutterFlowTheme.of(context).title2.copyWith(fontSize: 16, color: Colors.black),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                );
              } else {
                return const SizedBox();
              }
            },
          ),
        ),
      ),
    );
  }

  Scaffold _buildPaymentTrueMoneySection() {
    return Scaffold(
      body: SafeArea(
        bottom: false,
        child: SingleChildScrollView(
          child: BlocBuilder<PaymentOncheckCubit, PaymentOncheckState>(
            builder: (context, state) {
              if (state is PaymentOncheckLoaded) {
                return SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            // 'assets/images/bags.png',
                            UImageAssets.BAGS,
                            fit: BoxFit.cover,
                            scale: 2,
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      const Text('ขอบคุณสำหรับคำสั่งซื้อ!', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 5),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
                            width: 60,
                            height: 60,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: Image.asset(
                                // 'assets/images/app_launcher_icon.png',
                                UImageAssets.IC_APP_LAUNCHER,
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            child: Icon(CupertinoIcons.arrow_right_arrow_left, color: Colors.blue.shade700, size: 16),
                          ),
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: const [BoxShadow(color: Colors.black45, blurRadius: 0.5)],
                            ),
                            width: 60,
                            height: 60,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(20),
                              child: Image.network('https://www.truemoney.com/wp-content/uploads/2020/11/logo-truemoneywallet-300x300-1.jpg'),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 5),
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 32),
                        child: Text(
                          'คุณชำระเงินผ่านช่องทาง True Money Wallet กรุณาดำเนินการต่อที่แอพพลิเคชั่นของ True Money Wallet',
                          style: TextStyle(fontSize: 16),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 10),
                      const Padding(
                        padding: EdgeInsets.only(left: 16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [Text('รายละเอียดคำสั่งซื้อ', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold))],
                        ),
                      ),
                      const SizedBox(height: 10),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: state.carts.length,
                          itemBuilder: (context, index) {
                            final shop = state.carts[index];
                            final List<Products> selectedProductList = state.selectedProductList.where((element) => element.shopId == shop.shopId).toList();
                            return Column(
                              children: [
                                GestureDetector(
                                  child: Row(
                                    children: [
                                      Icon(Icons.storefront_outlined, color: Colors.orange.shade700),
                                      const SizedBox(width: 5),
                                      Text(shop.shopName.toString(), style: const TextStyle(fontWeight: FontWeight.bold)),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 5),
                                ListView.builder(
                                  itemCount: selectedProductList.length,
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemBuilder: (context, indexP) {
                                    final product = selectedProductList[indexP];
                                    final List<int> pid = selectedProductList.map((e) => int.parse(e.id.toString())).toList();

                                    return Padding(
                                      padding: const EdgeInsets.only(bottom: 11),
                                      child: Column(
                                        children: [
                                          Row(
                                            children: [
                                              ClipRRect(
                                                borderRadius: BorderRadius.circular(5),
                                                child: Image.network(
                                                  product.thumbnail.toString(),
                                                  width: 70,
                                                  height: 70,
                                                  errorBuilder: (BuildContext context, Object exception, StackTrace? stackTrace) {
                                                    return Image.asset(
                                                      // 'assets/images/image_def-resize.png',
                                                      // width: double.infinity,
                                                      UImageAssets.DEF_RESIZE,
                                                      width: 70,
                                                      height: 70,
                                                      fit: BoxFit.contain,
                                                    );
                                                  },
                                                ),
                                              ),
                                              const SizedBox(width: 10),
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  mainAxisAlignment: MainAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      product.name.toString(),
                                                      maxLines: 2,
                                                      overflow: TextOverflow.ellipsis,
                                                      style: const TextStyle(fontWeight: FontWeight.w500),
                                                    ),
                                                    Text(
                                                      product.optionName != null ? "${product.optionName} : ${product.optionValue}" : 'No Brand',
                                                      style: FlutterFlowTheme.of(
                                                        context,
                                                      ).bodyText1.override(fontFamily: 'Sarabun', fontSize: 12, fontWeight: FontWeight.normal, useGoogleFonts: false),
                                                    ),
                                                    Row(
                                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                      children: [
                                                        Row(
                                                          crossAxisAlignment: CrossAxisAlignment.center,
                                                          children: [
                                                            Text(
                                                              "฿${product.discountPrice == 0 ? product.price.toString() : product.discountPrice.toString()} ",
                                                              style: FlutterFlowTheme.of(context).bodyText1.override(
                                                                fontFamily: 'Sarabun',
                                                                color: const Color(0xFF0073E6),
                                                                fontSize: 14,
                                                                fontWeight: FontWeight.bold,
                                                                useGoogleFonts: false,
                                                              ),
                                                            ),
                                                            Text(
                                                              product.discountPrice != product.price ? '฿${product.price.toString()}' : '',
                                                              maxLines: 2,
                                                              style: FlutterFlowTheme.of(context).bodyText1.override(
                                                                fontFamily: 'Sarabun',
                                                                color: const Color(0xFF91929D),
                                                                fontSize: 14,
                                                                fontWeight: FontWeight.w700,
                                                                decoration: TextDecoration.lineThrough,
                                                                useGoogleFonts: false,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                        Row(
                                                          children: [
                                                            Text(
                                                              'จำนวน : ',
                                                              style: FlutterFlowTheme.of(
                                                                context,
                                                              ).bodyText1.override(fontFamily: 'Sarabun', fontSize: 14, fontWeight: FontWeight.w700, useGoogleFonts: false),
                                                            ),
                                                            Text(product.productQty.toString()),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                          indexP == selectedProductList.length - 1 ? const SizedBox() : const Divider(),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                                const Divider(),
                              ],
                            );
                          },
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            color: Colors.white,
                            border: Border.all(color: Colors.black54),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'รวมค่าสินค้า',
                                      style: FlutterFlowTheme.of(context).subtitle2.override(fontFamily: 'Sarabun', color: const Color(0xff9098B1), useGoogleFonts: false),
                                    ),
                                    Text('฿${FormatNumbers().format(state.checkoutPrice.toString() ?? '0.00')}', style: FlutterFlowTheme.of(context).subtitle2),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'ค่าจัดส่ง',
                                      style: FlutterFlowTheme.of(context).subtitle2.override(fontFamily: 'Sarabun', color: const Color(0xff9098B1), useGoogleFonts: false),
                                    ),
                                    Text('฿${FormatNumbers().format(state.shippingPrice.toString())}', style: FlutterFlowTheme.of(context).subtitle2),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'ส่วนลด',
                                      style: FlutterFlowTheme.of(context).subtitle2.override(fontFamily: 'Sarabun', color: const Color(0xff9098B1), useGoogleFonts: false),
                                    ),
                                    Text('฿${FormatNumbers().format(state.discount.toString())}', style: FlutterFlowTheme.of(context).subtitle2),
                                  ],
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'ยอดรวมคำสั่งซื้อ',
                                      style: FlutterFlowTheme.of(context).subtitle2.override(fontFamily: 'Sarabun', color: const Color(0xff9098B1), useGoogleFonts: false),
                                    ),
                                    Text('฿${FormatNumbers().format(state.amoutPrice.toString())}', style: FlutterFlowTheme.of(context).subtitle2),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: InkWell(
                          onTap: () async {
                            await Navigator.pushAndRemoveUntil(context, CupertinoPageRoute(builder: (context) => const NavBarPage(initialPage: 0)), (r) => false);
                          },
                          child: Container(
                            width: double.infinity,
                            height: 60,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              image: DecorationImage(
                                fit: BoxFit.cover,
                                image: Image.asset(
                                  // 'assets/images/button_bg.png',
                                  UImageAssets.BUTTON_BG,
                                ).image,
                              ),
                              borderRadius: BorderRadius.circular(14),
                            ),
                            child: Align(
                              alignment: const AlignmentDirectional(0, 0),
                              child: Text(
                                'ดูสินค้าต่อ',
                                textAlign: TextAlign.center,
                                style: FlutterFlowTheme.of(context).title2.override(fontFamily: 'Sarabun', color: Colors.white, useGoogleFonts: false),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: InkWell(
                          onTap: () async {
                            context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: ''));
                            context.read<CartBloc>().add(GetCartEvent());
                            // Navigator.push(context, CupertinoPageRoute(builder: (context) => const MyOrderPage()));
                            Navigator.pushReplacement(context, CupertinoPageRoute(builder: (context) => const MyOrderPage()));
                          },
                          child: Container(
                            width: double.infinity,
                            height: 60,
                            decoration: BoxDecoration(color: const Color(0xFFF5F5F5), borderRadius: BorderRadius.circular(14)),
                            child: Align(
                              alignment: const AlignmentDirectional(0, 0),
                              child: Text('ไปหน้าออเดอร์', textAlign: TextAlign.center, style: FlutterFlowTheme.of(context).title2),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                );
              } else {
                return const SizedBox();
              }
            },
          ),
        ),
      ),
    );
  }

  Widget _buildBodyPaymentPurchase(PaymentPurchaseState state, bool iBanking, BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.only(top: 10, left: 10, right: 10),
            decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(10.0)),
            padding: const EdgeInsets.all(10.0),
            child: Column(
              children: [
                Row(
                  children: [
                    const Text(
                      'เลขที่สั่งซื้อ : ',
                      style: TextStyle(color: Colors.black87, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SelectableText(
                      '#${state.paymentpurchase.data!.id}',
                      style: const TextStyle(color: Colors.black87, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                  ],
                ),
                Row(
                  children: [
                    const Text(
                      'TXN : ',
                      style: TextStyle(color: Colors.black87, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SelectableText(
                      '${state.paymentpurchase.data!.txn}',
                      style: const TextStyle(color: Colors.black87, fontWeight: FontWeight.normal, fontSize: 14),
                    ),
                  ],
                ),
                const Row(
                  children: [
                    Text(
                      'สถานะ: ',
                      style: TextStyle(color: Colors.black87, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SelectableText(
                      'รอการชำระเงิน',
                      style: TextStyle(color: Colors.black87, fontWeight: FontWeight.normal, fontSize: 14),
                    ),
                  ],
                ),
                5.gap,
                Row(
                  children: [
                    const Text(
                      'ยอดเงินที่ต้องชำระ: ',
                      style: TextStyle(color: Colors.black87, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    SelectableText(
                      '฿${formatNumberWithDot(state.paymentpurchase.data?.totalAmount ?? 0)}',
                      style: const TextStyle(color: ColorThemeConfig.primaryColor, fontWeight: FontWeight.bold, fontSize: 18),
                    ),
                  ],
                ),
              ],
            ),
          ),
          iBanking
              ? CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () async {
                    _handleIBankingPayment(state, context);
                  },
                  child: Container(
                    margin: const EdgeInsets.all(8.0),
                    decoration: BoxDecoration(color: ColorThemeConfig.primaryColor, borderRadius: BorderRadius.circular(8)),
                    width: double.infinity,
                    alignment: Alignment.center,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: Text(
                      'ดำเนินการชำระเงิน',
                      style: Theme.of(context).textTheme.bodyLarge!.copyWith(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ),
                )
              : Container(
                  margin: const EdgeInsets.all(10.0),
                  decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(10.0)),
                  padding: const EdgeInsets.all(10.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(flex: 2, child: Image.asset('assets/icons/scb-logo.png', scale: 8)),
                          Expanded(
                            flex: 6,
                            child: Column(
                              children: [
                                const Row(
                                  children: [
                                    Expanded(
                                      child: SelectableText(
                                        'ธนาคารไทยพาณิชย์ จำกัด (มหาชน) (SCB)',
                                        style: TextStyle(color: Colors.black87, fontWeight: FontWeight.bold, fontSize: 14),
                                      ),
                                    ),
                                  ],
                                ),
                                const Row(
                                  children: [
                                    Text(
                                      'ชื่อบัญชี : ',
                                      style: TextStyle(color: Colors.black87, fontWeight: FontWeight.bold, fontSize: 14),
                                    ),
                                    SelectableText(
                                      'บริษัท ช้อป ชิลล์ จำกัด',
                                      style: TextStyle(color: Colors.black87, fontWeight: FontWeight.normal, fontSize: 14),
                                    ),
                                  ],
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    const Text(
                                      'เลขที่บัญชี: ',
                                      style: TextStyle(color: Colors.black87, fontWeight: FontWeight.bold, fontSize: 14),
                                    ),
                                    const Expanded(
                                      child: SelectableText(
                                        '164-407316-0',
                                        style: TextStyle(color: Colors.black87, fontWeight: FontWeight.bold, fontSize: 16),
                                      ),
                                    ),
                                    const SizedBox(width: 5),
                                    GestureDetector(
                                      onTap: () {
                                        Clipboard.setData(const ClipboardData(text: "164-407316-0"));
                                        showSnackbar(context, "คัดลอกเลขบัญชีสำเร็จ");
                                      },
                                      child: const Icon(Icons.copy, size: 20),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const Divider(),
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text('วันที่โอน', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
                                5.gap,
                                TextFormField(
                                  controller: state.dateController,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'กรุณาเลือกวันที่';
                                    } else {
                                      return null;
                                    }
                                  },
                                  onTap: () async {
                                    final DateTime? pickedDate = await showDatePicker(
                                      context: context,
                                      initialDate: DateTime.now(),
                                      firstDate: DateTime.utc(2021, 01, 01),
                                      lastDate: DateTime.now(),
                                    );

                                    if (pickedDate != null && pickedDate != DateTime.now()) {
                                      // Format the picked date
                                      final String formattedDate = DateFormat('dd-MM-yyyy').format(pickedDate);

                                      context.read<PaymentCubit>().ondateController(date: formattedDate);
                                      print('Selected date: $formattedDate');
                                      formKey.currentState!.validate();
                                    }
                                  },
                                  readOnly: true,
                                  decoration: InputDecoration(
                                    hintText: 'dd-mm-yyyy',
                                    contentPadding: const EdgeInsets.symmetric(horizontal: 10),
                                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                                    suffixIcon: const Icon(Icons.calendar_today_rounded),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text('เวลาที่โอน', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
                                5.gap,
                                TextFormField(
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'กรุณาเลือกเวลา';
                                    } else {
                                      return null;
                                    }
                                  },
                                  controller: state.timeController,
                                  onTap: () async {
                                    final TimeOfDay? selectedTime = await showTimePicker(context: context, initialTime: TimeOfDay.now());

                                    if (selectedTime != null) {
                                      context.read<PaymentCubit>().ontimeController(time: selectedTime.format(context));
                                      print('Selected time: ${selectedTime.format(context)}');
                                      formKey.currentState!.validate();
                                    }
                                  },
                                  readOnly: true,
                                  decoration: InputDecoration(
                                    hintText: '--:--',
                                    contentPadding: const EdgeInsets.symmetric(horizontal: 10),
                                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                                    suffixIcon: const Icon(Icons.access_time),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 15),
                      const Text('ภาพสลิป', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 10),
                      state.image == ''
                          ? Material(
                              color: Colors.white,
                              child: InkWell(
                                onTap: () async {
                                  // systemAlertSelectImage(context);
                                  final XFile? photo = await ImagePicker().pickImage(source: ImageSource.gallery, maxHeight: 800, maxWidth: 800);
                                  final File file = File(photo!.path);
                                  context.read<PaymentCubit>().purchaseUploadImageGallery(file: file);
                                  //Navigator.pop(context);
                                },
                                child: Container(
                                  decoration: BoxDecoration(color: Colors.grey.shade100, borderRadius: BorderRadius.circular(8)),
                                  width: MediaQuery.of(context).size.width,
                                  height: 160,
                                  //color: Colors.grey.shade300,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const Icon(Icons.image, color: Colors.black54),
                                      const SizedBox(width: 10),
                                      Text('อัปโหลดรูปสลิป', style: Theme.of(context).textTheme.bodyLarge!.copyWith(fontSize: 14, color: Colors.black54)),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          : Material(
                              color: Colors.white,
                              child: Stack(
                                alignment: Alignment.bottomRight,
                                children: [
                                  Container(
                                    decoration: BoxDecoration(color: Colors.grey.shade100, borderRadius: BorderRadius.circular(8)),
                                    width: MediaQuery.of(context).size.width,
                                    height: 400,
                                    //color: Colors.grey.shade300,
                                    child: Center(
                                      child: GestureDetector(
                                        onTap: () {
                                          //Navigator.pushNamed(context, '/photo-widget', arguments: path);
                                        },
                                        child: Image.network(
                                          state.image,
                                          fit: BoxFit.contain,
                                          loadingBuilder: (BuildContext context, Widget child, ImageChunkEvent? loadingProgress) {
                                            if (loadingProgress == null) {
                                              return child;
                                            }
                                            return Center(
                                              child: CircularProgressIndicator(
                                                value: loadingProgress.expectedTotalBytes != null
                                                    ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                                                    : null,
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: () async {
                                      final XFile? photo = await ImagePicker().pickImage(source: ImageSource.gallery, maxHeight: 800, maxWidth: 800);
                                      final File file = File(photo!.path);
                                      context.read<PaymentCubit>().purchaseUploadImageGallery(file: file);
                                      //_showActionSheet();
                                    },
                                    child: Container(
                                      margin: const EdgeInsets.only(right: 5, bottom: 5),
                                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                                      decoration: BoxDecoration(color: Colors.white.withValues(alpha: .9), borderRadius: BorderRadius.circular(8)),
                                      child: Text('เลือกภาพใหม่', style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold)),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                      const SizedBox(height: 15),
                    ],
                  ),
                ),
          if (!iBanking)
            CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: state.isLoading
                  ? () {}
                  : () {
                      if (formKey.currentState!.validate()) {
                        if (state.image == '' || state.image.isEmpty) {
                          ShopChillLoading.showError('กรุณาอัปโหลดรูปสลิป');
                          return;
                        }
                        _showDialogSureToSendSlip(context, state);
                      }
                    },
              child: Container(
                margin: const EdgeInsets.all(8.0),
                decoration: BoxDecoration(color: ColorThemeConfig.primaryColor, borderRadius: BorderRadius.circular(8)),
                width: double.infinity,
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: Text(
                  state.isLoading ? 'กำลังอัพโหลด..' : 'ยืนยันอัพโหลด',
                  style: Theme.of(context).textTheme.bodyLarge!.copyWith(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          const SizedBox(height: 50),
        ],
      ),
    );
  }

  void _navigateToMyOrder(BuildContext context) {
    context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: ''));
    Navigator.pushAndRemoveUntil(
      context,
      CupertinoPageRoute(builder: (context) => MyOrderPage(selectIndex: int.parse(widget.selectIndex.toString()))),
      (route) => route.settings.name == AppRoutes.navBar || route.settings.name == AppRoutes.home,
    );
  }

  Future<void> _handleIBankingPayment(PaymentPurchaseState state, BuildContext context) async {
    try {
      final response = await CheckOutRepository().paymentMobileBanking(paymentId: int.parse((widget.pid).toString()));

      if (response['status'] == true) {
        ShopChillLoading.dismiss();

        final String redirectUrl = response['data']['redirect_url'] ?? '';
        final uri = Uri.tryParse(redirectUrl);
        try {
          await launchUrl(uri!);
          _navigateToMyOrder(context);
          return;
        } catch (e) {
          // ถ้าเปิดไม่ได้ fallback ไปยัง Store
          final fallbackUrl = BankStoreRedirectLink.getBankStoreRedirectLink(redirectUrl);
          final fallbackUri = Uri.tryParse(fallbackUrl);

          try {
            await launchUrl(fallbackUri!);
            _navigateToMyOrder(context);
            return;
          } catch (e) {
            _navigateToMyOrder(context);
            ShopChillLoading.showError("ไม่สามารถดำเนินการ Mobile Banking ได้");
            return;
          }
        }
      } else {
        ShopChillLoading.showError(response['message'] ?? "ไม่สามารถดำเนินการ Mobile Banking ได้");
        _navigateToMyOrder(context);
      }
    } catch (e, t) {
      debugPrintStack(label: "err : $e", stackTrace: t);
    } finally {
      ShopChillLoading.dismiss();
    }
    /*   ShopChillLoading.show(status: 'กำลังเตรียมการชำระเงิน');
    final response = await CheckOutRepository().paymentMobileBanking(paymentId: int.parse((widget.pid).toString()));
    print('data for direct to bank = $response');

    if (response['status'] == true) {
      ShopChillLoading.dismiss();
      final String redirectUrl = response['data']['redirect_url'];

      if (await canLaunchUrl(Uri.parse(redirectUrl))) {
        await launchUrl(Uri.parse(redirectUrl));
      } else {
        final String appStoreUrl = BankStoreRedirectLink.getBankStoreRedirectLink(redirectUrl);
        print("appStoreUrl => $appStoreUrl");
        await launchUrl(Uri.parse(appStoreUrl));
        // throw 'Could not launch $customUrl';
      }

      context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: ''));
      Navigator.push(context, MaterialPageRoute(builder: (context) => const MyOrderPage()));
      return;
    } else {
      ShopChillLoading.showError(response['message']);
      //emit(state.copyWith(status: CheckoutStatus.failure));
    } */
  }

  Future<bool?> _showBackDialog(BuildContext context) async {
    return showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return CustomAlertDialog(
          title: 'มีการเปลี่ยนแปลงที่ยังไม่ได้บันทึก',
          description: 'คุณต้องการออกจากการชำระเงินที่ยังไม่เสร็จ?',
          onCancel: () => Navigator.of(context, rootNavigator: true).pop(),
          btnConfirmText: 'ยืนยัน',
          onConfirm: () async {
            if (widget.goToRouterName == AppRoutes.orderDetail) {
              context.read<OrderBloc>().add(GetOrderDetailEvent(widget.orderId));
              Navigator.of(dialogContext, rootNavigator: true).pop(true); // ปิด dialog
              Navigator.pop(context);
              return;
            }

            context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: ''));
            context.read<CartBloc>().add(GetCartEvent());

            Navigator.pushNamed(context, AppRoutes.navBar, arguments: 0);

            Navigator.pushAndRemoveUntil(context, CupertinoPageRoute(builder: (context) => MyOrderPage(selectIndex: int.parse(widget.selectIndex.toString()))), (route) {
              print("route.settings.name ${route.settings.name}");
              return route.settings.name == AppRoutes.navBar || route.settings.name == AppRoutes.home;
            });
          },
        );
      },
    );
  }

  void _showDialogSureToSendSlip(BuildContext context, PaymentPurchaseState state) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return CustomAlertDialog(
          title: 'ยืนยันอัพโหลดสลิป',
          description: 'ท่านได้ตรวจสอบยอดที่ต้องชำระทั้งหมด\n฿${formatNumberWithDot(state.paymentpurchase.data?.totalAmount ?? 0)} กับสลิปที่แนบแล้ว?',
          onCancel: () => Navigator.of(context, rootNavigator: true).pop(),
          btnConfirmText: 'ยืนยัน',
          onConfirm: () async {
            context.read<PaymentCubit>().onSendSlip(
              paymentId: state.paymentpurchase.data!.id!,
              slip: state.image,
              date: state.dateController.text,
              time: state.timeController.text,
              context: context,
            );
          },
        );
      },
    );
  }

  AppBar _buildAppBarPaymentPurchase(String title, BuildContext context) {
    return AppBar(
      centerTitle: true,
      title: CustomText(text: title, fontSize: 18, fontWeight: FontWeight.bold),
      backgroundColor: Colors.white,
      automaticallyImplyLeading: false,
      elevation: 0,
      actions: [
        IconButton(
          onPressed: () {
            _showBackDialog(context);
          },
          icon: const Icon(Icons.close, color: Colors.black),
        ),
        const SizedBox(width: 10),
      ],
    );
  }

  AppBar _buildAppBarPaymentQr(BuildContext context) {
    return AppBar(
      centerTitle: true,
      title: const CustomText(text: 'รอการชำระเงิน', fontSize: 18, fontWeight: FontWeight.bold),
      backgroundColor: Colors.white,
      automaticallyImplyLeading: false,
      elevation: 0,
      actions: [
        IconButton(
          onPressed: () {
            /*context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: ''));
            context.read<CartBloc>().add(GetCartEvent());
            Navigator.pushReplacement(context, CupertinoPageRoute(builder: (context) => const MyOrderPage()));*/
            /*  if (widget.goToRouterName == AppRoutes.myOrder) {
              context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: ''));
              context.read<CartBloc>().add(GetCartEvent());
              Navigator.pushReplacement(
                  context,
                  CupertinoPageRoute(
                      builder: (context) => MyOrderPage(
                            selectIndex: int.parse(widget.selectIndex.toString()),
                          )));
            } else {
              context.read<OrderBloc>().add(GetOrderDetailEvent(widget.orderId));
              Navigator.pop(context);
            } */

            _showBackDialog(context);
          },
          icon: const Icon(Icons.close, color: Colors.black),
        ),
        const SizedBox(width: 10),
      ],
    );
  }

  Widget _buildBodyPaymentQr(PaymentQrState state, BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          RepaintBoundary(
            key: previewContainer,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(color: FlutterFlowTheme.of(context).secondaryBackground),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  QRCodeWithLogo(
                    qrImageData: state.qrdecode,
                    topLogoPath: UImageAssetsQRCode.QR_CODE_TOP_COLOR,
                    subLogoPath: UImageAssetsQRCode.QR_CODE_LAN_COLOR,
                    centerLogoPath: UImageAssetsQRCode.QR_CODE_CENTER_COLOR,
                  ),
                  Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                    child: CustomText(text: 'THB ${formatNumberWithDot(double.parse(state.paymentqr.totalAmount ?? '0'))}', fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(12, 8, 12, 20),
            decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(10.0)),
            margin: const EdgeInsets.all(10),
            child: Column(
              children: [
                10.gap,
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(UImageAssetsQRCode.QR_CODE_CENTER_COLOR, width: 30, height: 30),
                    const SizedBox(width: 10),
                    const CustomText(text: 'คิวอาร์พร้อมเพย์', fontSize: 16, fontWeight: FontWeight.w600),
                  ],
                ),
                10.gap,
                const Divider(height: 1),
                15.gap,
                CartPromotionTextRowWidget(
                  fontSize: 14,
                  prefix: 'ยอดเงินที่ต้องชำระ: ',
                  valueColor: ColorThemeConfig.primaryColor,
                  value: '฿${formatNumberWithDot(double.parse(state.paymentqr.totalAmount ?? '0'))}',
                ),
                CartPromotionTextRowWidget(
                  fontSize: 14,
                  prefix: 'ชำระเงินให้เสร็จสมบูรณ์ก่อน: ',
                  value: '${DateFormat.MMMd('th').format(state.paymentqr.expriedTime!)} เวลา ${DateFormat.Hm().format(state.paymentqr.expriedTime!)}',
                ),
                CartPromotionTextRowWidget(fontSize: 14, prefix: 'หมดอายุใน: ', valueColor: Colors.grey, value: state.remainingTime),
                // const Divider(height: 5),
              ],
            ),
          ),
          CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () async {
              try {
                ShopChillLoading.show(status: 'กำลังบันทึก..');
                final RenderRepaintBoundary boundary = previewContainer.currentContext!.findRenderObject() as RenderRepaintBoundary;

                final image = await boundary.toImage(pixelRatio: 10);
                final ByteData? byteData = await image.toByteData(format: ImageByteFormat.png);
                final Uint8List pngBytes = byteData!.buffer.asUint8List();

                final appDir = await getApplicationDocumentsDirectory();

                final datetime = DateTime.now();

                file = await File('${appDir.path}/$datetime.png').create();

                await file?.writeAsBytes(pngBytes);
                ImageGallerySaverPlus.saveFile(file!.path).then((success) {
                  ShopChillLoading.dismiss();
                  setState(() {
                    print('บันทึกแล้ว');
                    ShopChillLoading.showSuccess('บันทึกสำเร็จ');
                  });
                });
              } catch (e) {
                ShopChillLoading.dismiss();
                print(e.toString());
              }
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 20),
              decoration: BoxDecoration(color: ColorThemeConfig.primaryColor, borderRadius: BorderRadius.circular(8)),
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'บันทึกคิวอาร์โค้ด',
                    style: Theme.of(context).textTheme.bodyLarge!.copyWith(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(width: 10),
                  const Icon(Icons.file_download_rounded, color: Colors.white),
                ],
              ),
            ),
          ),
          /* Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  child: CupertinoButton(
                    onPressed: () {
                      */
          /*final notificationState = context.read<NotificationBloc>();
                      final state = notificationState.state;
                      if (state.getEnableNotification && state.getOrderPaymentSuccessNotification) {
                        NotificationService().sendNotification(
                          title: 'แจ้งเตือน',
                          body: "สั่งซื้อสำเร็จ 🎁🎁🎁",
                        );
                      }
                      Navigator.pushReplacement(context, CupertinoPageRoute(
                        builder: (context) {
                          return PaymentOnCheckWidget();
                        },
                      ));*/
          /*
                    },
                    padding: EdgeInsets.zero,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.blue.shade700,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'โอนแล้ว',
                              style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),*/
          const SizedBox(height: 50),
        ],
      ),
    );
  }
}
