import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop_chill_app/app_routers.dart';
import 'package:shop_chill_app/app_state.dart';
import 'package:shop_chill_app/backend/api_requests/domain.dart';
import 'package:shop_chill_app/screens/checkout/repository/check_out_repository.dart';
import 'package:shop_chill_app/screens/checkout/widgets/paysolution_webview.dart';
import 'package:shop_chill_app/screens/my_account/repository/user_repository.dart';
import 'package:shop_chill_app/screens/my_cart/bloc/cart_bloc/cart_bloc.dart';
import 'package:shop_chill_app/screens/myorder/my_order_page.dart';
import 'package:shop_chill_app/screens/order/bloc/order_bloc.dart';
import 'package:shop_chill_app/screens/payment/model/payment_cod_model.dart';
import 'package:shop_chill_app/screens/payment/model/payment_credit_response.dart';
import 'package:shop_chill_app/screens/payment/model/payment_expire_model.dart';
import 'package:shop_chill_app/screens/payment/model/payment_purchase_model.dart';
import 'package:shop_chill_app/screens/payment/model/payment_truemoney_model.dart';
import 'package:shop_chill_app/screens/payment/model/payment_waiting_model.dart';
import 'package:shop_chill_app/screens/payment/model/paymentt_qr_model.dart';
import 'package:shop_chill_app/screens/paymentmethod/repository/payment_repository.dart';
import 'package:shop_chill_app/shered/enum/payment_gatway_type.dart';
import 'package:shop_chill_app/shered/enum/payment_provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../config/shopchill_loading/shopchill_loading.dart';

part 'payment_state.dart';

class PaymentCubit extends Cubit<PaymentState> {
  final CheckOutRepository _checkOutRepository = CheckOutRepository();

  PaymentCubit() : super(PaymentLoading());
  Timer? _timer;

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }

  void startCountdown(DateTime expiredTime) {
    _timer?.cancel(); // Cancel existing timer
    _updateTime(expiredTime);

    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      _updateTime(expiredTime);
    });
  }

  void _updateTime(DateTime expiredTime) {
    final state = this.state;
    if (state is PaymentQrState) {
      final DateTime now = DateTime.now();
      final Duration diff = expiredTime.difference(now);

      if (diff.isNegative) {
        _timer?.cancel();
        emit(state.copyWith(remainingTime: "00:00", isExpired: true));

        //emit(PaymentLoading());
        // state.paymentqr.expriedTime = null;
        reGenerateQr(masterOrderId: state.masterorderId, paymentId: state.paymentId, gatewayId: state.gatewayId, txn: state.txn);
      } else {
        emit(state.copyWith(remainingTime: _formatDuration(diff)));
      }
    }
  }

  String _formatDuration(Duration duration) {
    final int hours = duration.inHours;
    final int minutes = duration.inMinutes % 60;
    final int seconds = duration.inSeconds % 60;

    if (hours > 0) {
      return "${_twoDigits(hours)}:${_twoDigits(minutes)}:${_twoDigits(seconds)}";
    } else {
      return "${_twoDigits(minutes)}:${_twoDigits(seconds)}";
    }
  }

  String _twoDigits(int n) => n.toString().padLeft(2, '0');

  void reGenerateQr({required int masterOrderId, required int paymentId, required int gatewayId, required String txn}) async {
    emit(PaymentLoading());
    try {
      await CheckOutRepository().reGenerateQr(masterorderId: masterOrderId.toString());
      onCheckPayment(
        paymentType: PaymentGatewayType.qrcode,
        masterOrderId: masterOrderId.toString(),
        paymentId: paymentId.toString(),
        gatewayId: gatewayId.toString(),
        orderNo: txn,
        provider: null,
        context: null,
      );
    } catch (e, t) {
      debugPrint("Error in reGenerateQr: $e\n$t");
    }
  }

  void _flashPayCardPayment({required String masterOrderId}) async {
    final PaymentCreditResponse payment = await _checkOutRepository.getCreditModel(paymentId: masterOrderId);
    emit(PaymentCreditcardState(provider: PaymentProvider.flashpay.code));

    await launchUrl(Uri.parse(payment.data?.redirectUrl ?? ''));
  }

  void _paysolutionCardPayment(BuildContext context, {required int? paymentId}) async {
    final getPayment = await _checkOutRepository.getpayment(paymentid: paymentId ?? 0);
    final masterOrderId = '${getPayment.data?.orders.first.masterOrderId ?? ''}';

    final String url = '${domain.domain}/api/payment/pay/credit-card?payment_id=$masterOrderId&api_token=${FFAppState().token}';

    emit(PaymentCreditcardState(provider: PaymentProvider.flashpay.code));

    Navigator.push(context, CupertinoPageRoute(builder: (context) => PaysolutionWebview(url: url)));
  }

  void _handledCreditCardPayment({required BuildContext context, required String masterOrderId, required String provider, required int? paymentId}) async {
    if (provider == PaymentProvider.flashpay.code) {
      _flashPayCardPayment(masterOrderId: masterOrderId);
    } else if (provider == PaymentProvider.paysolution.code) {
      _paysolutionCardPayment(context, paymentId: paymentId);
    }
  }

  void _handledOtherPayment({
    required PaymentGatewayType paymentType,
    required String masterOrderId,
    required String paymentId,
    required String gatewayId,
    required String orderNo,
  }) async {
    final dynamic paymentModel = await _checkOutRepository.postPayment(paymentType: paymentType, paymentId: paymentId, masterOrderId: masterOrderId);
    if (paymentModel is PaymentExpireModel) {
      emit(PaymentExpireState(expire: paymentModel));
    } else if (paymentModel is PaymentQrModel) {
      final Uint8List bytes = base64Decode(paymentModel.checkoutUrl!);
      emit(
        PaymentQrState(
          paymentqr: paymentModel,
          qrdecode: bytes,
          gatewayId: int.parse(gatewayId),
          paymentId: int.parse(paymentId),
          masterorderId: int.parse(masterOrderId),
          txn: orderNo,
        ),
      );
    } else if (paymentModel is PaymentPurchaseModel) {
      emit(PaymentPurchaseState(paymentpurchase: paymentModel, image: '', dateController: TextEditingController(), timeController: TextEditingController(), isLoading: false));
    } else if (paymentModel is PaymentCodModel) {
      emit(PaymentCodState(cod: paymentModel));
    } else if (paymentModel is PaymentWaitingModel) {
      emit(PaymentWaitingState(waiting: paymentModel));
    } else if (paymentModel is PaymentTrueMoneyModel) {
      if (paymentModel.status == true) {
        emit(PaymentTrueMoneyState(truemoney: paymentModel));
        await launchUrl(Uri.parse(paymentModel.data!.link!), mode: LaunchMode.externalApplication);
      }
      emit(PaymentTrueMoneyState(truemoney: paymentModel));
    } else {
      emit(PaymentLoadErrorState(message: paymentModel['msg'] ?? 'เกิดข้อผิดพลาดบางอย่าง'));
    }
  }

  void onCheckPayment({
    required BuildContext? context,
    required PaymentGatewayType paymentType,
    required String masterOrderId,
    required String paymentId,
    required String gatewayId,
    required String orderNo,
    required String? provider,
  }) async {
    emit(PaymentLoading());

    try {
      switch (paymentType) {
        case PaymentGatewayType.creditCard:
          _handledCreditCardPayment(context: context!, masterOrderId: masterOrderId, provider: provider!, paymentId: int.parse(paymentId));

          break;
        case PaymentGatewayType.ibanking:
        case PaymentGatewayType.cod:
        case PaymentGatewayType.qrcode:
        case PaymentGatewayType.transfer:
          _handledOtherPayment(paymentType: paymentType, masterOrderId: masterOrderId, paymentId: paymentId, gatewayId: gatewayId, orderNo: orderNo);
          break;
        case PaymentGatewayType.unknown:
          break;
      }
    } catch (e, t) {
      debugPrintStack(label: "err : $e", stackTrace: t);
      await Future.delayed(const Duration(milliseconds: 2000));
      emit(const PaymentLoadErrorState(message: 'เกิดข้อผิดพลาดบางอย่าง'));
      ShopChillLoading.dismiss();
    }
  }

  /*  void onCheckPayment({
    required PaymentGatewayType paymentType,
    required String masterorderId,
    required String paymentId,
    required String gatewayId,
    required String orderNo,
  }) async {
    print('paymentType =  $paymentType');
    print('cubit master id =  $masterorderId');
    print('gatewayId id =  $gatewayId');
    emit(PaymentLoading());
    final TextEditingController dateController = TextEditingController();
    final TextEditingController timeController = TextEditingController();
    try {
      if (gatewayId == '5') {
        final PaymentCreditResponse payment = await CheckOutRepository().getCreditModel(paymentId: masterorderId);
        emit(PaymentCreditcardState(credit: payment));

        await launchUrl(Uri.parse(payment.data?.redirectUrl ?? ''));
      } else {
        final dynamic paymentModel = await CheckOutRepository().postPayment(paymentType: paymentType, paymentId: paymentId, masterOrderId: masterorderId);
        print('paymentModel = $paymentModel');
        if (paymentModel is PaymentExpireModel) {
          emit(PaymentExpireState(expire: paymentModel));
        } else if (paymentModel is PaymentQrModel) {
          print('state qr');
          final Uint8List bytes = base64Decode(paymentModel.checkoutUrl!);
          emit(
            PaymentQrState(
              paymentqr: paymentModel,
              qrdecode: bytes,
              gatewayId: int.parse(gatewayId),
              paymentId: int.parse(paymentId),
              masterorderId: int.parse(masterorderId),
              txn: orderNo,
            ),
          );
        } else if (paymentModel is PaymentPurchaseModel) {
          print('state pur');
          emit(PaymentPurchaseState(paymentpurchase: paymentModel, image: '', dateController: dateController, timeController: timeController, isLoading: false));
        } else if (paymentModel is PaymentTrueMoneyModel) {
          print('state truemoney');

          if (paymentModel.status == true) {
            emit(PaymentTrueMoneyState(truemoney: paymentModel));
            // Navigator.pushReplacement(context, CupertinoPageRoute(
            //   builder: (context) {
            //     return PaymentCompleteWidget();
            //   },
            // ));
            await launchUrl(Uri.parse(paymentModel.data!.link!), mode: LaunchMode.externalApplication);
          }
          emit(PaymentTrueMoneyState(truemoney: paymentModel));
        } else if (paymentModel is PaymentCodModel) {
          /*NotificationService().sendNotification(
                title: 'แจ้งเตือน',
                body: "สั่งซื้อสำเร็จ 🎁🎁🎁",
              );*/
          // Navigator.pushReplacement(context, CupertinoPageRoute(
          //   builder: (context) {
          //     return PaymentCompleteWidget();
          //   },
          // ));
          emit(PaymentCodState(cod: paymentModel));
        } else if (paymentModel is PaymentWaitingModel) {
          print('state waiting');
          emit(PaymentWaitingState(waiting: paymentModel));
        } else {
          emit(PaymentLoadErrorState(message: paymentModel['msg'] ?? 'เกิดข้อผิดพลาดบางอย่าง'));
        }
      }
    } catch (e, t) {
      debugPrintStack(label: "err : $e", stackTrace: t);
      await Future.delayed(const Duration(milliseconds: 2000));
      emit(const PaymentLoadErrorState(message: 'เกิดข้อผิดพลาดบางอย่าง'));
      ShopChillLoading.dismiss();
    }
  }
 */
  void purchaseUploadImageGallery({required File file}) async {
    final state = this.state;
    if (state is PaymentPurchaseState) {
      final String image = await UserRepository().uploadImage(file: file);
      emit(state.copyWith(image: image));
    }
  }

  void ondateController({required String date}) {
    final state = this.state;
    if (state is PaymentPurchaseState) {
      state.dateController.text = date;

      // Emit the updated state with the modified controller
      emit(state.copyWith(dateController: state.dateController));
    }
  }

  void ontimeController({required String time}) {
    final state = this.state;
    if (state is PaymentPurchaseState) {
      state.timeController.text = time;
      emit(state.copyWith(timeController: state.timeController));
    }
  }

  void onSendSlip({required int paymentId, required String slip, required String date, required String time, required BuildContext context}) async {
    final state = this.state;
    if (state is PaymentPurchaseState) {
      print('on cubit');
      // emit(state.copyWith(isLoading: true));
      ShopChillLoading.show(status: 'กำลังอัพโหลด');
      await Future.delayed(const Duration(seconds: 1));
      await PaymentRepository().uploadSlip(paymentId: paymentId, slip: slip, date: date, time: time).then((value) {
        if (value != false) {
          //emit(state.copyWith(isLoading: false));
          ShopChillLoading.showSuccess('อัพโหลดสำเร็จ');
          context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: ''));
          context.read<CartBloc>().add(GetCartEvent());
          // Navigator.pushReplacement(context, CupertinoPageRoute(builder: (context) => const MyOrderPage()));
          Navigator.pushAndRemoveUntil(
            context,
            CupertinoPageRoute(builder: (context) => const MyOrderPage()),
            (route) => route.settings.name == AppRoutes.navBar || route.settings.name == AppRoutes.home,
          );
        } else {
          // emit(state.copyWith(isLoading: false));
          ShopChillLoading.dismiss();
          ShopChillLoading.showError('อัพโหลดไม่สำเร็จ');
        }
      });
    }
  }
}
