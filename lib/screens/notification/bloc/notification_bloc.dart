import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop_chill_app/screens/my_account/bloc/user/user_bloc.dart';
import 'package:shop_chill_app/screens/notification/models/notification_order_model.dart';

import '../../order/model/order_model.dart';
import '../../order/repository/order_repository.dart';
import '../models/notification_setting_model.dart';
import '../notification_service.dart';
import '../repository/notification_repository.dart';

part 'notification_event.dart';

part 'notification_state.dart';

class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  final OrderRepository _orderRepository = OrderRepository();

  final NotificationRepository _notificationRepository = NotificationRepository();

  NotificationBloc() : super(const NotificationState()) {
    on<NotificationInitialEvent>((_mapNotificationInitialEventToState));
    on<NotificationEnableNotificationEvent>((_mapNotificationEnableNotificationEventToState));
    on<NotificationEnableSoundNotificationEvent>((_mapNotificationEnableSoundNotificationEventToState));

    on<NotificationOrdersAndShippingNotificationEvent>((_mapNotificationOrdersAndShippingNotificationEventToState));

    on<NotificationChatNotificationEvent>((_mapNotificationChatNotificationEventToState));

    on<NotificationPromotionNotificationEvent>((_mapNotificationPromotionNotificationEventToState));

    on<NotificationOrderPaymentSuccessNotificationEvent>((_mapNotificationOrderPaymentSuccessNotificationEventToState));

    /// setting part
    on<NotificationInitialSettingEvent>((_notificationInitialSettingEvent));
  }

  void _notificationInitialSettingEvent(NotificationInitialSettingEvent event, Emitter<NotificationState> emit) async {
    final context = event.context;
    final userState = context.read<UserBloc>().state;

    if (userState is UserLoading) {
      const fx = false;
      emit(state.copyWith(
        enableNotification: true,
        enableSoundNotification: true,
        ordersAndShippingNotification: fx,
        chatNotification: fx,
        promotionNotification: true,
        orderPaymentSuccessNotification: fx,
      ));
      return;
    }

    final response = await _notificationRepository.notificationServices();
    if (response.data != null) {
      final data = response.data;
      final enableNotification = notificationCheck(data!.allNotice);
      final enableSoundNotification = notificationCheck(data.sound);
      final ordersAndShippingNotification = notificationCheck(data.order);
      final chatNotification = notificationCheck(data.chat);
      final promotionNotification = notificationCheck(data.promotion);
      final orderPaymentSuccessNotification = notificationCheck(data.createOrderSuccess);

      emit(state.copyWith(
        enableNotification: enableNotification,
        enableSoundNotification: enableSoundNotification,
        ordersAndShippingNotification: ordersAndShippingNotification,
        chatNotification: chatNotification,
        promotionNotification: promotionNotification,
        orderPaymentSuccessNotification: orderPaymentSuccessNotification,
      ));
    } else {
      const int x = 1;
      await _notificationRepository.notificationSetting(
        allNotice: x,
        sound: x,
        order: x,
        chat: x,
        promotion: x,
        createOrderSuccess: x,
      );
      add(NotificationInitialSettingEvent(event.context));
    }
  }

  bool notificationCheck(int? value) {
    return value == 1;
  }

  int notificationCheckInt(bool? value) {
    return value == true ? 1 : 0;
  }

  // NotificationInitialEvent
  void _mapNotificationInitialEventToState(NotificationInitialEvent event, Emitter<NotificationState> emit) async {
    // List<OrderOrder> orders = [];
    try {
      emit(state.copyWith(isLoadingOrder: true));
      final NotificationOrderModel orderData = await _notificationRepository.getNotificationOrders();
      emit(state.copyWith(order: orderData, isLoadingOrder: false));
    } catch (e, t) {
      log(e.toString());
      log(t.toString());
      emit(state.copyWith(isLoadingOrder: false));
      throw Exception();
    }
  }

  // EnableNotification
  void _mapNotificationEnableNotificationEventToState(NotificationEnableNotificationEvent event, Emitter<NotificationState> emit) async {
    emit(state.copyWith(enableNotification: event.value));

    if (state.getEnableNotification) {
      await _notificationRepository.notificationSetting(
        allNotice: 1,
        sound: notificationCheckInt(state.enableSoundNotification),
        order: notificationCheckInt(state.ordersAndShippingNotification),
        chat: notificationCheckInt(state.chatNotification),
        promotion: notificationCheckInt(state.promotionNotification),
        createOrderSuccess: notificationCheckInt(state.orderPaymentSuccessNotification),
      );
    } else {
      const x = false;

      await _notificationRepository.notificationSetting(
        allNotice: 0,
        sound: 0,
        order: 0,
        chat: 0,
        promotion: 0,
        createOrderSuccess: 0,
      );
      emit(state.copyWith(
        enableNotification: x,
        enableSoundNotification: x,
        ordersAndShippingNotification: x,
        chatNotification: x,
        promotionNotification: x,
        orderPaymentSuccessNotification: x,
      ));
    }
  }

  // EnableSoundNotification
  void _mapNotificationEnableSoundNotificationEventToState(NotificationEnableSoundNotificationEvent event, Emitter<NotificationState> emit) async {
    if (state.getEnableNotification) {
      isSound = event.value;
      emit(state.copyWith(enableSoundNotification: event.value));
      await _notificationRepository.notificationSetting(
        allNotice: 1,
        sound: notificationCheckInt(event.value),
        order: notificationCheckInt(state.ordersAndShippingNotification),
        chat: notificationCheckInt(state.chatNotification),
        promotion: notificationCheckInt(state.promotionNotification),
        createOrderSuccess: notificationCheckInt(state.orderPaymentSuccessNotification),
      );
    }
  }

  // OrdersAndShippingNotification
  void _mapNotificationOrdersAndShippingNotificationEventToState(NotificationOrdersAndShippingNotificationEvent event, Emitter<NotificationState> emit) async {
    if (state.getEnableNotification) {
      emit(state.copyWith(ordersAndShippingNotification: event.value));
      await _notificationRepository.notificationSetting(
        allNotice: 1,
        sound: notificationCheckInt(state.enableSoundNotification),
        order: notificationCheckInt(event.value),
        chat: notificationCheckInt(state.chatNotification),
        promotion: notificationCheckInt(state.promotionNotification),
        createOrderSuccess: notificationCheckInt(state.orderPaymentSuccessNotification),
      );
    }
  }

  // ChatNotification
  void _mapNotificationChatNotificationEventToState(NotificationChatNotificationEvent event, Emitter<NotificationState> emit) async {
    if (state.getEnableNotification) {
      emit(state.copyWith(chatNotification: event.value));
      await _notificationRepository.notificationSetting(
        allNotice: 1,
        sound: notificationCheckInt(state.enableSoundNotification),
        order: notificationCheckInt(state.ordersAndShippingNotification),
        chat: notificationCheckInt(event.value),
        promotion: notificationCheckInt(state.promotionNotification),
        createOrderSuccess: notificationCheckInt(state.orderPaymentSuccessNotification),
      );
    }
  }

  // PromotionNotification
  void _mapNotificationPromotionNotificationEventToState(NotificationPromotionNotificationEvent event, Emitter<NotificationState> emit) async {
    if (state.getEnableNotification) {
      emit(state.copyWith(promotionNotification: event.value));
      await _notificationRepository.notificationSetting(
        allNotice: 1,
        sound: notificationCheckInt(state.enableSoundNotification),
        order: notificationCheckInt(state.ordersAndShippingNotification),
        chat: notificationCheckInt(state.chatNotification),
        promotion: notificationCheckInt(event.value),
        createOrderSuccess: notificationCheckInt(state.orderPaymentSuccessNotification),
      );
    }
  }

  // OrderPaymentSuccessNotification
  void _mapNotificationOrderPaymentSuccessNotificationEventToState(NotificationOrderPaymentSuccessNotificationEvent event, Emitter<NotificationState> emit) async {
    if (state.getEnableNotification) {
      emit(state.copyWith(orderPaymentSuccessNotification: event.value));
      await _notificationRepository.notificationSetting(
        allNotice: 1,
        sound: notificationCheckInt(state.enableSoundNotification),
        order: notificationCheckInt(state.ordersAndShippingNotification),
        chat: notificationCheckInt(state.chatNotification),
        promotion: notificationCheckInt(state.promotionNotification),
        createOrderSuccess: notificationCheckInt(event.value),
      );
    }
  }
}
