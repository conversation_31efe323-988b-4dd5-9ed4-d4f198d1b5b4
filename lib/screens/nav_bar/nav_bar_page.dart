import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/screens/my_account/bloc/user/user_bloc.dart';
import 'package:shop_chill_app/screens/my_cart/bloc/cart_bloc/cart_bloc.dart';
import 'package:shop_chill_app/screens/my_review/bloc/myreview_bloc.dart';
import 'package:shop_chill_app/screens/nav_bar/tabs.dart';
import 'package:shop_chill_app/screens/notification/bloc/notification_bloc.dart';
import 'package:shop_chill_app/screens/order/bloc/order_count_cubit/order_count_cubit.dart';
import 'package:shop_chill_app/screens/video/bloc/feed_video/feed_video_bloc_bloc.dart';
import 'package:shop_chill_app/screens/video/bloc/feed_video/feed_video_bloc_event.dart';
import 'package:shop_chill_app/shered/util/scroll_controllers.dart';
import 'package:shop_chill_app/shered/widgets/dialog/custom_alert_updart.dart';

class NavBarPage extends StatefulWidget {
  const NavBarPage({super.key, required this.initialPage, this.feedId});

  final int? feedId;
  final int initialPage;

  @override
  _NavBarPageState createState() => _NavBarPageState();
}

class _NavBarPageState extends State<NavBarPage> {
  late int _currentPageIndex;
  int? feed;
  final GlobalKey<CustomUpgradeAlertState> _upgradeAlertKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    print('initState NavBar');

    context.read<CartBloc>().add(GetCartEvent());
    _currentPageIndex = widget.initialPage;

    feed = widget.feedId;
    print('🚀 CASE2-FEED-ID 1=> ${widget.feedId}');
  }

  void _handleTabSelection(int selectedIndex) async {
    setState(() {
      _currentPageIndex = selectedIndex;
    });
    _triggerBlocEventForSelectedTab(selectedIndex);
  }

  int doubleTapIndex = 0;
  void onGoToTop() {
    if (_currentPageIndex == 0) {
      doubleTapIndex++;
    }

    if (doubleTapIndex == 2) {
      ScrollControllers.homeScrollToTop(context, RefreshController(initialRefresh: false));
      doubleTapIndex = 0;
    }

    print('doubleTapIndex: $doubleTapIndex');
  }

  void _triggerBlocEventForSelectedTab(int index) {
    switch (index) {
      case 0:
        //home
        onGoToTop();
        // ScrollControllers.homeScrollToTop(context);

        feed = null;
        break;
      case 2:
        doubleTapIndex = 0;
        //mall
        // context.read<MallBloc>().add(GetMallEvent());
        context.read<FeedVideoBloc>().add(FetchFeeds(feed: feed));
        // context.read<DeepLinkBloc>().add(ConsumePendingLink());
        feed = null;
        context.read<CartBloc>().add(GetCartEvent());

        break;
      // case 3:
      //   context.read<LiveBloc>().add(LiveInitialEvent());
      //   break;
      case 3:
        //noti
        doubleTapIndex = 0;
        context.read<NotificationBloc>().add(NotificationInitialEvent());
        feed = null;
        break;

      case 4:
        doubleTapIndex = 0;
        context.read<UserBloc>().add(GetUserProfile());
        context.read<MyreviewBloc>().add(MyreviewInitialEvent());
        context.read<OrderCountCubit>().fetchOrderCount();

        feed = null;
        break;
      default:
        doubleTapIndex = 0;
        feed = null;
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomUpgradeAlert(
      key: _upgradeAlertKey,
      child: Scaffold(
        body: SafeArea(
          top: false,
          child: IndexedStack(index: _currentPageIndex, children: Tabs.getTabContent(_currentPageIndex, feed)),
        ),
        extendBody: true,
        bottomNavigationBar: _buildBottomNavigationBar(),
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      currentIndex: _currentPageIndex,
      onTap: _handleTabSelection,
      elevation: 10,
      backgroundColor: Colors.white,
      type: BottomNavigationBarType.fixed,
      // selectedItemColor: FlutterFlowTheme.of(context).primaryColor,
      selectedItemColor: ColorThemeConfig.newPrimaryColor,
      unselectedItemColor: Colors.black38,
      selectedFontSize: 10.5,
      unselectedFontSize: 10.5,
      showSelectedLabels: true,
      showUnselectedLabels: true,
      items: Tabs.getNavigationBarItems(_currentPageIndex, context),
    );
  }
}
