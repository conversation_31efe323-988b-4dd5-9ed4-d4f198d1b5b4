import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shop_chill_app/app_routers.dart';
import 'package:shop_chill_app/app_state.dart';
import 'package:shop_chill_app/backend/api_requests/api_calls.dart';
import 'package:shop_chill_app/config/color_theme_config.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_theme.dart';
import 'package:shop_chill_app/screens/flutter_flow/flutter_flow_util.dart';
import 'package:shop_chill_app/screens/my_cart/bloc/cart_bloc/cart_bloc.dart';
import 'package:shop_chill_app/screens/my_cart/repository/my_cart_repository.dart';
import 'package:shop_chill_app/screens/order/bloc/order_bloc.dart';
import 'package:shop_chill_app/screens/order/model/order_detail_new_model.dart';
import 'package:shop_chill_app/screens/order/model/order_model.dart';
import 'package:shop_chill_app/screens/order/repository/order_repository.dart';
import 'package:shop_chill_app/screens/order/review_detail/bloc/review_detail_bloc.dart';
import 'package:shop_chill_app/screens/payment/payment_cubit/payment_cubit.dart';
import 'package:shop_chill_app/screens/product_page/product_detail_page.dart';
import 'package:shop_chill_app/screens/reviews/pages/review_order/review_order_page.dart';
import 'package:shop_chill_app/screens/shop/bloc/shop_bloc.dart';
import 'package:shop_chill_app/screens/shop/bloc/shop_tab_bar_bloc.dart';
import 'package:shop_chill_app/screens/video/widgets/custom_image_network.dart';
import 'package:shop_chill_app/shered/assets/image_assets.dart';
import 'package:shop_chill_app/shered/enum/payment_gatway_type.dart';
import 'package:shop_chill_app/shered/util/custom_text.dart';
import 'package:shop_chill_app/shered/util/format_number.dart';
import 'package:shop_chill_app/shered/util/format_numbers.dart';
import 'package:shop_chill_app/shered/util/loading_reviewdetail_shimmer.dart';
import 'package:shop_chill_app/shered/util/payment_gatway_type_utils.dart';
import 'package:shop_chill_app/shered/util/product/product_price_utils.dart';
import 'package:shop_chill_app/shered/widgets/placeholder_message_widget.dart';

import '../../../config/shopchill_loading/shopchill_loading.dart';
import '../../product_page/model/product_detail_model.dart';
import '../../reviews/pages/review_order/my_review_order_page.dart';
import 'custom_preview_product_more_bloc.dart';

class MyOrderItemWidget extends StatefulWidget {
  const MyOrderItemWidget({super.key, required this.masterOrders, required this.status, required this.state});

  final List<MasterOrders> masterOrders;
  final String status;
  final OrderLoaded state;

  @override
  _MyOrderItemWidgetState createState() => _MyOrderItemWidgetState();
}

class _MyOrderItemWidgetState extends State<MyOrderItemWidget> with TickerProviderStateMixin {
  int countAllOrders = 0;

  var allReasons;
  int selectedReasons = 1;

  void cancelOrderCall(masterOrderId, status) async {
    showLoading();
    var shouldSetState = false;
    final cancelOrderResponse = await CancelOrderCall.call(masterOrderId: masterOrderId, cancelReason: selectedReasons, token: FFAppState().token);
    shouldSetState = true;
    hideLoading();
    if (CancelOrderCall.status((cancelOrderResponse.jsonBody ?? ''))) {
      if (shouldSetState) {
        setState(() {
          context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: status.toString()));
        });
      }
      return;
    } else {
      showError(CancelOrderCall.msg((cancelOrderResponse.jsonBody ?? '')));
    }

    setState(() {});
  }

  void cancelOrderReasonsCall(masterOrderId, status) async {
    var shouldSetState = false;
    ShopChillLoading.show(status: 'กำลังโหลด...');
    final cancelOrderReasonsResponse = await CancelOrderReasonsCall.call(token: FFAppState().token);
    shouldSetState = true;
    if (CancelOrderReasonsCall.status((cancelOrderReasonsResponse.jsonBody ?? ''))) {
      if (shouldSetState) {
        setState(() {
          final reasonsList = CancelOrderReasonsCall.reasons((cancelOrderReasonsResponse.jsonBody ?? '')).toList();
          allReasons = reasonsList.where((element) => element['type'] == 'order').toList();
          ShopChillLoading.dismiss();
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return _MyDialog(
                reasons: allReasons,
                onSelectedReasonsListChanged: (reasons) {
                  print(reasons);
                  setState(() {
                    selectedReasons = reasons;
                    print(selectedReasons);
                    cancelOrderCall(masterOrderId, status);
                  });
                },
              );
            },
          );
        });
      }
      ShopChillLoading.dismiss();
      return;
    } else {
      ShopChillLoading.dismiss();
      showError(CancelOrderReasonsCall.msg((cancelOrderReasonsResponse.jsonBody ?? '')));
    }

    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    // _firstLoad();
    //_controller = ScrollController()..addListener(_loadMore);
    context.read<PreviewProductMoreBloc>().add(PreviewProductInitEvent());

    /// context.read<OrderBloc>().add(GetOrderByIdTabEvent(widget.status.toString()));
  }

  @override
  void dispose() {
    // _controller.removeListener(_loadMore);
    super.dispose();
  }

  final reviewWidgetKey = GlobalKey<FormState>();

  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  List<OrderData> sortOrderItemsByReceivedAt(List<OrderData> orders) {
    final sortedOrders = List<OrderData>.from(orders);

    sortedOrders.sort((a, b) {
      // เงื่อนไข: ถ้า status == 5 ถึงจะเรียง
      if (a.status == 5 && b.status == 5) {
        final DateTime? receivedA = _parseDateTime(a.receivedAt);
        final DateTime? receivedB = _parseDateTime(b.receivedAt);

        // null จะถือว่าเป็นหลังสุด
        if (receivedA == null && receivedB == null) return 0;
        if (receivedA == null) return 1;
        if (receivedB == null) return -1;

        // เรียงล่าสุด → เก่าสุด (descending)
        return receivedB.compareTo(receivedA);
      }

      // ถ้า status ไม่ใช่ 5 → ไม่เรียง (คงลำดับเดิม)
      return 0;
    });

    return sortedOrders;
  }

  DateTime? _parseDateTime(String? date) {
    if (date == null || date.isEmpty) return null;
    return DateTime.tryParse(date);
  }

  @override
  Widget build(BuildContext context) {
    return SmartRefresher(
      controller: _refreshController,
      enablePullDown: true,
      enablePullUp: widget.state.orderModel.isMoreEnabled,
      header: const MaterialClassicHeader(),
      onRefresh: () async {
        context.read<OrderBloc>().add(ClearOrderEvent());

        context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: widget.status));
        context.read<OrderBloc>().add(OnchangePageEvent(status: widget.status));
        await context.read<OrderBloc>().stream.first;
        _refreshController.refreshCompleted();
      },
      onLoading: () async {
        context.read<OrderBloc>().add(LoadMore());
        await context.read<OrderBloc>().stream.first;
        _refreshController.loadComplete();
      },
      child: widget.masterOrders.isNotEmpty
          ? Builder(
              builder: (context) {
                return ListView.builder(
                  padding: const EdgeInsets.only(bottom: 20, top: 10, left: 6, right: 6),
                  shrinkWrap: true,
                  scrollDirection: Axis.vertical,
                  physics: const BouncingScrollPhysics(),
                  itemCount: widget.masterOrders.length,
                  itemBuilder: (context, masterindex) {
                    final orders = widget.masterOrders[masterindex].orders ?? [];
                    final paymentStatus = widget.masterOrders[masterindex].paymentStatus ?? 0;
                    final isPayment = paymentStatus == 1;

                    return _buildMasterOrderItem(context, orders, masterindex, isPayment);
                  },
                );
              },
            )
          : const PlaceholderMessageWidget(icon: FontAwesomeIcons.basketShopping, message: 'ยังไม่มีรายการคำสั่งซื้อในสถานะนี้'),
    );
  }

  Widget _buildMasterOrderItem(BuildContext context, List<OrderData> orders, int index, bool isPayment) {
    final sortedItems = sortOrderItemsByReceivedAt(orders);

    return Card(
      elevation: 0,
      color: isPayment ? Colors.transparent : Colors.white,
      margin: const EdgeInsets.only(bottom: 10),

      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: ListView.builder(
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        scrollDirection: Axis.vertical,
        physics: const BouncingScrollPhysics(),
        itemCount: sortedItems.length,
        itemBuilder: (context, orderIndex) {
          final orderItem = sortedItems[orderIndex];
          final hasMasterOrder = sortedItems.length > 1;

          return _buildOrderItem(context, orderItem, index, hasMasterOrder, sortedItems, isPayment);
        },
      ),
    );
  }

  Widget _buildOrderItem(BuildContext context, OrderData orderItem, int index, bool hasMasterOrder, List<OrderData> oders, bool isPayment) {
    return Card(
      elevation: 0,
      color: Colors.white,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: GestureDetector(
        // borderRadius: BorderRadius.circular(8),
        // onTap: () => _onViewDetail(orderItem, false),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _shopSection(context, orderItem.shop, orderItem),
              Visibility(
                visible: orderItem.status != 3 && orderItem.tracking.isNotEmpty,
                child: GestureDetector(
                  onTap: () async {
                    ShopChillLoading.show(status: 'กำลังโหลด...');
                    final orderDetail = await OrderRepository().fetchOrderDetail(orderItem.id.toString());
                    ShopChillLoading.dismiss();
                    Navigator.pushNamed(context, AppRoutes.orderTracking, arguments: orderDetail.data);
                  },
                  child: Container(
                    width: double.infinity,
                    margin: const EdgeInsets.only(top: 8, bottom: 5),
                    padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black38,
                          blurRadius: .1,
                          spreadRadius: 0.1,
                          // offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(FontAwesomeIcons.boxOpen, size: 14, color: Colors.blue.shade800),
                            const SizedBox(width: 8),
                            const CustomText(text: 'พัสดุของคุณได้รับการส่งมอบแล้ว', fontSize: 14, fontWeight: FontWeight.w600),
                          ],
                        ),
                        const Icon(FontAwesomeIcons.chevronRight, size: 12, color: Colors.black54),
                      ],
                    ),
                  ),
                ),
              ),
              _productListWidget(orderItem, index),
              _buildOrderSummary(orderItem, context),
              const Padding(padding: EdgeInsets.symmetric(horizontal: 8), child: Divider()),
 _buildActionButton(orderItem, context),
            ],
          ),
        ),
      ),
    );
  }

  final Set<int> _expandedIndices = {};

  Widget _productListWidget(OrderData orderItem, int masterindex) {
    final isExpanded = _expandedIndices.contains(masterindex);
    final visibleCount = isExpanded ? orderItem.orderDetails.length : 2;
    return BlocBuilder<PreviewProductMoreBloc, PreviewProductMoreBlocState>(
      builder: (context, state) {
        return Column(
          children: [
            AnimatedSize(
              duration: const Duration(milliseconds: 500),
              curve: Curves.ease,
              alignment: Alignment.topCenter,
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                scrollDirection: Axis.vertical,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: visibleCount.clamp(0, orderItem.orderDetails.length),
                itemBuilder: (context, ordersIndex) {
                  final item = orderItem.orderDetails[ordersIndex];

                  /*    String sellPrice = '';
                  String discountPrice = '';

                  sellPrice = calculatePrice(item.productSellPrice.toDouble(), item.productDiscount.toDouble());
                  discountPrice = calculateDiscount(
                    item.productSellPrice.toDouble(),
                    item.productSellPrice.toDouble() > item.productDiscount.toDouble() ? item.productDiscount.toDouble() : 0,
                  ); */
                  final priceResult = ProductPriceUtils.instance.getProductPrice(sellPrice: item.productSellPrice, discountPrice: item.productDiscount);
                  final sellPrice = priceResult.sellPrice;
                  final discountPrice = priceResult.originalPrice;

                  String thumbnail = '';
                  if (item.productOption?.thumbnail.isNotEmpty ?? false) {
                    thumbnail = item.productOption?.thumbnail ?? '';
                  } else {
                    thumbnail = item.product?.thumbnail ?? '';
                  }

                  return AnimatedSwitcher(
                    duration: const Duration(milliseconds: 500),
                    transitionBuilder: (child, animation) {
                      final slide = Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(animation);
                      final fade = CurvedAnimation(parent: animation, curve: Curves.easeInOut);
                      return FadeTransition(
                        opacity: fade,
                        child: SlideTransition(position: slide, child: child),
                      );
                    },
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 8),
                        // _buildCustomRowSection(label: 'ออเดอร์', value: orderItem.),
                        // if (orderItem.tracking.isNotEmpty)
                        //   _buildCustomRowSection(label: 'เลขพัสดุ', value: orderItem.tracking),
                        GestureDetector(
                          onTap: () => _navigateToProductDetail(item.product?.slug ?? '', thumbnail),
                          child: Container(
                            margin: EdgeInsets.only(bottom: orderItem.orderDetails.length == 1 ? 0 : 10),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  width: 75,
                                  height: 75,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    color: Colors.white,
                                    border: Border.all(color: Colors.grey.shade200),
                                  ),
                                  child: CustomImageNetwork(url: thumbnail, size: 75, borderRadius: BorderRadius.circular(8), fit: BoxFit.cover),
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  flex: 4,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                            flex: 7,
                                            child: Text(
                                              item.product?.name.toString() ?? '',
                                              style: FlutterFlowTheme.of(context).subtitle1.copyWith(fontWeight: FontWeight.w600, color: Colors.black87, fontSize: 13),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                          const SizedBox(width: 5),
                                        ],
                                      ),
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Visibility(
                                            visible: item.productOption != null,
                                            child: Container(
                                              margin: const EdgeInsets.only(top: 5, bottom: 5),
                                              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                                              decoration: BoxDecoration(color: Colors.grey.shade100, borderRadius: BorderRadius.circular(4)),
                                              child: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  CustomText(text: '${item.productOption?.name ?? ''} : ', fontSize: 12),
                                                  Flexible(
                                                    child: CustomText(
                                                      text: item.productOption?.value ?? '',
                                                      fontSize: 12,
                                                      overflow: TextOverflow.ellipsis,
                                                      textAlign: TextAlign.start,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          Text(
                                            'x${item.productQty.toString()}',
                                            style: Theme.of(context).textTheme.labelSmall?.copyWith(fontSize: 11, color: ColorThemeConfig.unfocusedTextColor),
                                          ),
                                        ],
                                      ),
                                      item.productDiscount == 0.0
                                          ? Text(
                                              '฿${formatNumberWithDot(double.parse(item.productSellPrice.toString()))}',
                                              style: FlutterFlowTheme.of(context).subtitle1.override(
                                                fontFamily: 'Sarabun',
                                                fontWeight: FontWeight.w600,
                                                color: Colors.blue.shade600,
                                                fontSize: 13,
                                                useGoogleFonts: false,
                                              ),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            )
                                          : Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Row(
                                                  children: [
                                                    if (discountPrice.isNotEmpty)
                                                      CustomText(
                                                        text: '฿${formatNumberWithDot(double.parse(discountPrice))}',
                                                        fontSize: 13,
                                                        color: Colors.grey,
                                                        decoration: TextDecoration.lineThrough,
                                                      ),
                                                    if (discountPrice.isNotEmpty) const SizedBox(width: 8),
                                                    CustomText(
                                                      text: '฿${formatNumberWithDot(double.parse(sellPrice))}',
                                                      fontSize: 13,
                                                      fontWeight: FontWeight.w600,
                                                      color: Colors.blue.shade800,
                                                    ),
                                                  ],
                                                ),
                                                CustomText(text: 'x${item.productQty.toString()}', fontSize: 13, color: Colors.black87, fontWeight: FontWeight.w600),
                                              ],
                                            ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        orderItem.isReceived == 1
                            ? item.reviewed != 1
                                  ? _buildNoReviewWidget(
                                      context,
                                      orderItem,
                                      // state,
                                      item,
                                    )
                                  : item.reviewed == 1
                                  ? _buildReviewed(context, item)
                                  : const SizedBox()
                            : const SizedBox(),
                        // Padding(padding: const EdgeInsets.symmetric(horizontal: 8), child: Divider())
                      ],
                    ),
                  );
                },
              ),
            ),
            // 5. สร้างปุ่ม "แสดงเพิ่มเติม" และซ่อนเมื่อแสดงครบแล้ว
            if (!isExpanded && orderItem.orderDetails.length > 3)
              CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  _expandedIndices.add(masterindex);
                  setState(() {});
                },
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomText(text: 'แสดงทั้งหมด', fontSize: 14, fontWeight: FontWeight.w600, color: ColorThemeConfig.primaryColor),
                    SizedBox(width: 2),
                    Icon(Icons.keyboard_arrow_down, size: 14, color: ColorThemeConfig.primaryColor),
                  ],
                ),
              ),
            if (isExpanded && orderItem.orderDetails.length > 3)
              CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  _expandedIndices.remove(masterindex);
                  setState(() {});
                },
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomText(text: 'แสดงน้อยลง', fontSize: 14, fontWeight: FontWeight.w600, color: ColorThemeConfig.primaryColor),
                    SizedBox(width: 2),
                    Icon(Icons.keyboard_arrow_up, size: 14, color: ColorThemeConfig.primaryColor),
                  ],
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildNoReviewWidget(
    BuildContext context,
    OrderData orderItem,
    // OrderLoaded state,
    OrderDetailModels orderDetail,
  ) {
    return Row(
      // mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        GestureDetector(
          onTap: () {
            /* showReviewModalBottomSheet(
              reviewWidgetKey: reviewWidgetKey,
              context: context,
              reviewImage: reviewImage,
              ratingPoint: ratingPoint,
              orderId: orderItem.id,
              reviewController: reviewController,
              status: widget.status,
              productThumbnail: orderDetail.product?.thumbnail ?? '',
              productName: orderDetail.product?.name ?? '',
              productQty: orderDetail.productQty,
              productOptionName: orderDetail.productOption?.name ?? '',
              productOptionValue: orderDetail.productOption?.value ?? '',
              productSellPrice: orderDetail.productSellPrice,
              productDiscount: orderDetail.productDiscount,
              productId: orderDetail.productId.toString(),
              productOptionId: orderDetail.productOptionId,
            );*/

            Navigator.push(
              context,
              CupertinoPageRoute(
                builder: (context) => ReviewOrderPage(
                  appBarTitle: 'เขียนรีวิว',
                  goToRouterName: AppRoutes.myOrder,
                  orderId: orderItem.id,
                  status: widget.status.isNotEmpty ? widget.status : '0',
                  productThumbnail: orderDetail.product?.thumbnail ?? '',
                  productName: orderDetail.product?.name ?? '',
                  productQty: orderDetail.productQty,
                  productOptionName: orderDetail.productOption?.name ?? '',
                  productOptionValue: orderDetail.productOption?.value ?? '',
                  productSellPrice: orderDetail.productSellPrice,
                  productDiscount: orderDetail.productDiscount,
                  productId: orderDetail.productId.toString(),
                  productOptionId: orderDetail.productOptionId.toString(),
                ),
              ),
            );
          },
          child: Card(
            margin: EdgeInsets.zero,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: const BorderSide(color: Colors.black45),
            ),
            child: Container(
              alignment: Alignment.center,
              // margin: const EdgeInsets.fromLTRB(0, 8, 8, 8),
              child: const Padding(
                padding: EdgeInsetsDirectional.symmetric(vertical: 4, horizontal: 16),
                child: CustomText(
                  text: 'เขียนรีวิว',
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  // color: Colors.white,
                  // color: FlutterFlowTheme.of(context).primaryText,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildReviewed(BuildContext context, OrderDetailModels orderDetail) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: GestureDetector(
            onTap: () {
              Navigator.push(context, CupertinoPageRoute(builder: (context) => MyReviewOrderPage(reviewId: orderDetail.reviewId)));
            },
            child: Card(
              margin: EdgeInsets.zero,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: const BorderSide(color: Colors.black45),
              ),
              child: Container(
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
                // margin: const EdgeInsets.fromLTRB(0, 8, 8, 8),
                child: CustomText(text: 'ดูคะแนน', fontSize: 14, fontWeight: FontWeight.w600, color: FlutterFlowTheme.of(context).primaryText),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<dynamic> _buildShowModalReviewPointWidget(BuildContext context) {
    return showModalBottomSheet(
      //showDragHandle: true,
      backgroundColor: Colors.white,
      elevation: 2,
      isScrollControlled: true,
      enableDrag: true,
      isDismissible: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24)),
      ),
      context: context,
      builder: (context) {
        return SafeArea(
          child: StatefulBuilder(
            builder: (BuildContext context, setState) {
              return FractionallySizedBox(
                heightFactor: 0.75,
                child: BlocBuilder<ReviewDetailBloc, ReviewDetailState>(
                  builder: (context, state) {
                    if (state is ReviewDetailLoading) {
                      return const LoadingReviewDetailShimmer();
                    } else if (state is ReviewDetailLoaded) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Center(
                            child: Padding(
                              padding: EdgeInsets.all(8.0),
                              child: CustomText(text: 'คะแนนสินค้า', fontSize: 20, fontWeight: FontWeight.bold),
                            ),
                          ),
                          const Divider(),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(8, 0, 3, 8),
                                child: Container(
                                  width: 70,
                                  height: 70,
                                  // use aligment
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: Image.network(
                                      state.review.data.first.productThumbnail ?? '',
                                      fit: BoxFit.cover,
                                      errorBuilder: (BuildContext context, Object exception, StackTrace? stackTrace) {
                                        return Image.asset(
                                          // 'assets/images/image_def-resize.png',
                                          UImageAssets.DEF_RESIZE,
                                        );
                                      },
                                      width: 70,
                                      height: 70,
                                      // fit: BoxFit.fitWidth,
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(8, 0, 16, 0),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Expanded(
                                            child: CustomText(
                                              text: state.review.data.first.productName.toString(),
                                              fontSize: 14,
                                              fontWeight: FontWeight.w600,
                                              color: FlutterFlowTheme.of(context).primaryText,
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ],
                                      ),
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Visibility(
                                            visible: state.review.data.first.optionName != null,
                                            child: Container(
                                              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                                              decoration: BoxDecoration(color: const Color(0xFFf2f2f2), borderRadius: BorderRadius.circular(5)),
                                              child: CustomText(
                                                text: "${state.review.data.first.optionName}",
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                                color: FlutterFlowTheme.of(context).secondaryText,
                                              ),
                                            ),
                                          ),
                                          /*CustomText(
                                            text: 'x${state.review.data.first.productQty}'.toString(),
                                            textAlign: TextAlign.end,
                                            fontSize: 12,
                                            color: FlutterFlowTheme.of(context).secondaryText,
                                          ),*/
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      RatingBar.builder(
                                        initialRating: state.review.data.first.rating!.toDouble(),
                                        ignoreGestures: true,
                                        minRating: 0,
                                        direction: Axis.horizontal,
                                        allowHalfRating: true,
                                        itemCount: 5,
                                        itemSize: 16,
                                        // itemPadding: EdgeInsets.symmetric(horizontal: 4),
                                        itemBuilder: (context, _) => const Icon(Icons.star, color: Colors.amber),
                                        onRatingUpdate: (rating) {
                                          // setState(() {
                                          //   orderDetail.ratingPoint = rating;
                                          //   print(orderDetail.ratingPoint);
                                          // });
                                        },
                                      ),
                                      /*Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          Row(
                                            children: [
                                              if (discountPrice.isNotEmpty)
                                                CustomText(
                                                  text: '฿${formatNumberWithDot(double.parse(discountPrice))}',
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.normal,
                                                  color: Colors.grey,
                                                  decoration: TextDecoration.lineThrough,
                                                ),
                                              SizedBox(width: 8),
                                              CustomText(
                                                text: '฿${formatNumberWithDot(double.parse(sellPrice))}',
                                                fontSize: 16,
                                                fontWeight: FontWeight.normal,
                                                color: Colors.blue,
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),*/
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),

                          Container(
                            margin: const EdgeInsets.symmetric(horizontal: 8),
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            decoration: BoxDecoration(borderRadius: BorderRadius.circular(5), color: const Color(0xFFf2f2f2)),
                            child: CustomText(text: state.review.data.first.details.toString()),
                          ),

                          // TextFormField(
                          //   controller: TextEditingController(text: state.review.data.first.),
                          //   minLines: 5,
                          //   maxLines: 5,
                          //   decoration: InputDecoration(
                          //       hintText: 'คำอธิบายประกอบการให้คะแนน',
                          //       border: OutlineInputBorder(
                          //           borderRadius: BorderRadius.circular(5),
                          //           borderSide: BorderSide(color: Colors.black54))),
                          // ),
                          const SizedBox(height: 8),
                          Container(
                            height: 120,
                            child: GridView.count(
                              childAspectRatio: 1,
                              scrollDirection: Axis.horizontal,
                              crossAxisSpacing: 1,
                              mainAxisSpacing: 1,
                              crossAxisCount: 1,
                              children: state.review.data.first.galleries.map((e) {
                                final int index = state.review.data.first.galleries.indexOf(e);
                                final List<Gallery> galleries = [];

                                state.review.data.first.galleries.forEach((element) {
                                  galleries.add(Gallery(url: element));
                                });

                                return GestureDetector(
                                  onTap: () {
                                    Navigator.pushNamed(context, AppRoutes.productImagePreview, arguments: {'galleries': galleries, 'startAtIndex': index});
                                  },
                                  key: ValueKey(e),
                                  child: Hero(
                                    tag: e.toString(),
                                    child: Container(
                                      margin: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade300,
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(color: Colors.grey.shade300, width: 1),
                                      ),
                                      child: CachedNetworkImage(
                                        imageUrl: e.toString(),
                                        fit: BoxFit.cover,
                                        placeholder: (context, url) => const CupertinoActivityIndicator(),
                                        errorWidget: (context, url, error) => Image.asset(
                                          'assets/image/ishipstore-icon.png',
                                          // scale: 50,
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                        ],
                      );
                    } else {
                      return const Text('error');
                    }
                  },
                ),
              );
            },
          ),
        );
      },
    );
  }

  List<String> reviewImage = [];
  TextEditingController reviewController = TextEditingController();
  double ratingPoint = 0;

  Widget _buildActionButton(OrderData orderItem, BuildContext context) {
    final payment = orderItem.payment;

    final allowedStatuses = {
      1, //รอการชำระเงิน
      17, //รอการตรวจสอบการชำระเงิน
    };
    final excludedPaymentTypes = {PaymentGatewayType.cod, PaymentGatewayType.transfer};
    final bool isTransfer = PaymentGatewayUtils.fromId(payment?.gatewayId) == PaymentGatewayType.transfer && orderItem.status == 1;

    final bool isPaymentButton = allowedStatuses.contains(orderItem.status) && !excludedPaymentTypes.contains(PaymentGatewayUtils.fromId(payment?.gatewayId)) || isTransfer;

    final bool isReturnParcel =
        orderItem.status == 5 && orderItem.isReceived == 0 && double.parse(orderItem.netTotalAmount) >= 200 && orderItem.refundEnabled == true && orderItem.refund == null;

    final bool isAddBankAccount = orderItem.status == 25;

    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        _buildViewDetailButton(context, orderItem),
        Row(
          children: [
            _buildPaymentButton(context, orderItem, isPaymentButton),
            // if(isPaymentButton || orderItem.status == 5 && orderItem.isReceived == 0)
            // SizedBox(width: 10),
            if (orderItem.status == 5 && orderItem.isReceived == 0)
              GestureDetector(
                onTap: () async {
                  showDialogRecieveParcel(context, orderItem.masterOrderId.toInt(), orderItem.id.toInt());
                },
                child: Container(
                  margin: const EdgeInsets.only(left: 10),
                  child: Card(
                    margin: EdgeInsets.zero,
                    elevation: 0,
                    color: FlutterFlowTheme.of(context).primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: BorderSide(color: Colors.blue.withOpacity(0.6)),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
                      child: Text(
                        'ฉันได้รับสินค้าแล้ว',
                        textAlign: TextAlign.center,
                        style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ),
              ),
            Visibility(
              visible: orderItem.isReceived != 0 && orderItem.status == 5,
              child: Row(
                children: [
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: () async {
                      try {
                        ShopChillLoading.show(status: 'กำลังโหลด...');
                        final List<String> orderIds = [];

                        for (var item in orderItem.orderDetails) {
                          orderIds.add(item.productId.toString());
                          await MyCartRepository().addToCart(
                            shopId: orderItem.shopId.toString(),
                            productId: item.productId.toString(),
                            productQty: '1',
                            productOptionId: 0,
                            inCampaignId: null,
                            productDiscountPrice: item.productOptionId,
                            productPrice: item.productSellPrice,
                            productTotalPrice: item.productDiscount,
                            // context: context,
                          );
                        }
                        ShopChillLoading.dismiss();
                        await Navigator.pushNamed(context, AppRoutes.myCart, arguments: orderIds);
                      } catch (e) {
                        print(e);
                      }
                    },
                    child: Card(
                      margin: EdgeInsets.zero,
                      elevation: 0,
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(color: Colors.blue.shade600),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.only(left: 20, right: 20, top: 7, bottom: 8),
                        child: CustomText(
                          text: 'ซื้ออีกครั้ง',
                          // fontSize: 14,
                          fontSize: 12,
                          color: Colors.blue.shade600,
                          textAlign: TextAlign.center,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            _buildRefundButton(context, orderItem, isReturnParcel),
            // if(orderItem.tracking.isEmpty)
            const SizedBox(width: 10),
            _buildAddBankAccountButton(context, orderItem),
            _buildReturnParcelButton(context, orderItem),
            _buildReturnParcelDetailButton(context, orderItem),
            _buildCancelOrderButton(context, orderItem),
          ],
        ),
      ],
    );
  }

  void showDialogRecieveParcel(BuildContext context, int masterorderId, int orderId) {
    final AlertDialog alert = AlertDialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 20),
      backgroundColor: Colors.white.withOpacity(.9),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: CustomText(
        text: 'ยืนยันการรับสินค้าและโอนเงินให้ร้านค้า',
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: FlutterFlowTheme.of(context).primaryText,
        maxLines: 2,
        // overflow: TextOverflow.ellipsis,
        textAlign: TextAlign.center,
      ),
      titleTextStyle: Theme.of(context).textTheme.titleLarge,
      titlePadding: const EdgeInsets.only(left: 20, top: 20, bottom: 5),
      contentPadding: EdgeInsets.zero,
      content: Container(
        padding: const EdgeInsets.all(16),
        child: CustomText(
          text: 'คุณไม่สามารถขอคืนเงินหรือคืนสินค้าได้ โปรดมั่นใจว่าคุณได้รับและตรวจสอบสินค้าแล้ว คุณยินยอมให้โอนเงินไปยังผู้ขาย',
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: FlutterFlowTheme.of(context).secondaryText,
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: CupertinoButton(
                  padding: EdgeInsets.zero,
                  color: Colors.grey,
                  child: Text(
                    'ยกเลิก',
                    style: FlutterFlowTheme.of(
                      context,
                    ).bodyText1.override(fontFamily: 'Sarabun', color: Colors.white, fontSize: 13, fontWeight: FontWeight.w500, useGoogleFonts: false),
                  ),
                  onPressed: () {
                    Navigator.of(context, rootNavigator: true).pop();
                  },
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: CupertinoButton(
                  padding: EdgeInsets.zero,
                  color: const Color(0xFF0056D6),
                  child: Text(
                    'ตกลง',
                    style: FlutterFlowTheme.of(
                      context,
                    ).bodyText1.override(fontFamily: 'Sarabun', color: Colors.white, fontSize: 13, fontWeight: FontWeight.w500, useGoogleFonts: false),
                  ),
                  onPressed: () async {
                    ShopChillLoading.show(status: 'กำลังโหลด...');
                    OrderRepository().recieveOrder(orderId: orderId).then((value) {
                      print('ยืนยันรับ :  $value');
                      ShopChillLoading.dismiss();
                      Navigator.pop(context);
                      // context.read<OrderBloc>().add(GetOrderDetailEvent(masterorderId.toString()));
                      //context.read<OrderBloc>().add(GetOrderDetailEvent(orderId.toString()));
                      context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: widget.status));
                    });
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
    // show the dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  Widget _buildReturnParcelDetailButton(BuildContext context, OrderData orderItem) {
    return orderItem.status == 20
        ? GestureDetector(
            onTap: () async {
              Navigator.pushNamed(
                context,
                AppRoutes.refundCfProductPage,
                arguments: orderItem.id.toString(),
                /*arguments: {
                  'orderId': orderItem.id.toString(),
                  'status': widget.status,
                },*/
              );
            },
            child: Card(
              margin: EdgeInsets.zero,
              elevation: 0,
              // color: Colors.grey.shade200,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),

              child: Container(
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey.shade200,
                  border: Border.all(color: Colors.grey.withOpacity(0.6)),
                ),
                // height: 40,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
                  child: Text(
                    'ดูเลขพัสดุส่งคืนสินค้า',
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          )
        : const SizedBox();
  }

  Widget _buildReturnParcelButton(BuildContext context, OrderData orderItem) {
    return orderItem.status == 24
        ? GestureDetector(
            onTap: () async {
              final is24 = orderItem.status == 24;
              context.read<OrderBloc>().add(GetOrderDetailEvent(orderItem.id.toString()));
              // await Future.delayed(const Duration(milliseconds: 500));
              await Navigator.pushNamed(context, AppRoutes.orderWidget, arguments: {'status': widget.status.toString(), 'isForceReturnParcel': is24});
              context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: widget.status));
            },
            child: Card(
              margin: EdgeInsets.zero,
              elevation: 1,
              color: Colors.grey.shade200,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                /*side: BorderSide(
                  color: Colors.red.withOpacity(0.6),
                ),*/
              ),

              // onTap: () async => cancelOrderReasonsCall(order.masterOrderId, order.status),
              child: Container(
                alignment: Alignment.center,
                // height: 40,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
                  child: Text(
                    'เลือกวิธีส่งคืนสินค้า',
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          )
        : const SizedBox();
  }

  Widget _buildAddBankAccountButton(BuildContext context, OrderData orderItem) {
    return orderItem.status == 25
        ? GestureDetector(
            onTap: () async {
              Navigator.pushNamed(context, AppRoutes.refundUpdatePayment, arguments: {'orderId': orderItem.id.toString(), 'status': widget.status});
            },
            child: Card(
              margin: EdgeInsets.zero,
              elevation: 1,
              color: Colors.grey.shade200,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                /*side: BorderSide(
                  color: Colors.red.withOpacity(0.6),
                ),*/
              ),

              // onTap: () async => cancelOrderReasonsCall(order.masterOrderId, order.status),
              child: Container(
                alignment: Alignment.center,
                // height: 40,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
                  child: Text(
                    'เพิ่มบัญชีคืนเงิน',
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          )
        : const SizedBox();
  }

  Widget _buildCancelOrderButton(BuildContext context, OrderData orderItem) {
    return (orderItem.tracking.isEmpty || orderItem.tracking == 'null') && orderItem.canceledAt == null
        ? GestureDetector(
            onTap: () async {
              _onViewDetail(orderItem, true);
              /*Navigator.push(
                context,
                CupertinoPageRoute(
                  builder: (context) => CancelOrderPage(
                    orderItem: orderItem,
                    status: widget.status.toString(),
                  ),
                ),
              );*/
            },
            child: Card(
              margin: EdgeInsets.zero,
              elevation: 0,
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: const BorderSide(color: Colors.grey),
              ),

              // onTap: () async => cancelOrderReasonsCall(order.masterOrderId, order.status),
              child: Container(
                alignment: Alignment.center,

                // height: 40,
                child: Padding(
                  padding: const EdgeInsets.only(left: 20, right: 20, top: 5, bottom: 8),
                  child: Text(
                    'ยกเลิก',
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          )
        : const SizedBox();
  }

  Widget _buildRefundButton(BuildContext context, OrderData orderItem, bool isRefundButton) {
    return isRefundButton
        ? GestureDetector(
            onTap: () async {
              showDialog(
                context: context,
                builder: (_) => AlertDialog(
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(CupertinoIcons.question_circle, color: Colors.teal, size: 100),
                      CustomText(
                        text: 'ยืนยันการคืนเงิน/คืนสินค้า',
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: FlutterFlowTheme.of(context).primaryText,
                        maxLines: 2,
                        // overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 10),
                      CustomText(
                        text: 'คุณสามารถยื่นคำขอคืนเงินหรือคืนสินค้าได้หลังจากรับสินค้าแล้วภายใน 3 วัน หรือภายใน ${orderItem.expiredRefundDate}',
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: FlutterFlowTheme.of(context).secondaryText,
                      ),
                      const SizedBox(height: 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Expanded(
                            child: CupertinoButton(
                              padding: const EdgeInsets.symmetric(horizontal: 16),
                              color: Colors.grey,
                              child: const CustomText(text: 'ยกเลิก', color: Colors.white),
                              onPressed: () => Navigator.pop(context),
                            ),
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: CupertinoButton(
                              padding: const EdgeInsets.symmetric(horizontal: 16),
                              color: ColorThemeConfig.primaryColor,
                              child: const CustomText(text: 'ยืนยัน', color: Colors.white),
                              onPressed: () async {
                                Navigator.pop(context);
                                await Navigator.pushNamed(
                                  context,
                                  AppRoutes.refundScreen,
                                  arguments: {'orderId': orderItem.id.toString(), 'status': widget.status, 'isLoadFullOption': true},
                                );
                                context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: widget.status));
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
            child: Container(
              margin: const EdgeInsets.only(left: 10),
              child: Card(
                margin: EdgeInsets.zero,
                elevation: 0,
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: Colors.teal.withOpacity(0.6)),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
                  child: Text(
                    'ขอคืนเงิน/คืนสินค้า',
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.teal, fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          )
        : const SizedBox();
  }

  Widget _buildPaymentButton(BuildContext context, OrderData orderItem, bool isPaymentButton) {
    return isPaymentButton
        ? GestureDetector(
            onTap: () async {
              context.read<PaymentCubit>().onCheckPayment(
                paymentType: PaymentGatewayUtils.fromId(orderItem.payment!.gatewayId),
                paymentId: orderItem.paymentId.toString(),
                masterOrderId: orderItem.masterOrderId.toString(),
                gatewayId: orderItem.payment!.gatewayId.toString(),
                provider: orderItem.payment?.gateway?.provider,
                orderNo: '',
                context: context,
              );
              //print(orderItem.toJson());

              await Navigator.pushNamed(
                context,
                AppRoutes.paymentScreen,
                arguments: {
                  "pid": orderItem.paymentId.toString(),
                  'masterOrderId': orderItem.masterOrderId.toString(),
                  'paymentId': orderItem.paymentId.toString(),
                  'gatewayId': orderItem.payment!.gatewayId.toString(),
                  'txn': '',
                  'goToRouterName': AppRoutes.myOrder,
                  'orderId': orderItem.id.toString(),
                  "selectIndex": widget.status,
                },
              );
            },
            child: Card(
              margin: const EdgeInsets.only(left: 10),
              elevation: 0,
              color: FlutterFlowTheme.of(context).primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(color: FlutterFlowTheme.of(context).primaryColor),
              ),
              child: Padding(
                padding: const EdgeInsets.only(left: 20, right: 20, top: 5, bottom: 8),
                child: Text(
                  'ชำระเงิน',
                  textAlign: TextAlign.center,
                  style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          )
        : const SizedBox();
  }

  Widget _buildViewDetailButton(BuildContext context, OrderData orderItem) {
    return GestureDetector(
      onTap: () => _onViewDetail(orderItem, false),
      child: Card(
        margin: EdgeInsets.zero,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: const BorderSide(color: Colors.black45),
        ),
        child: Padding(
          padding: const EdgeInsets.only(left: 20, right: 20, top: 5, bottom: 8),
          child: Text(
            'รายละเอียด',
            textAlign: TextAlign.center,
            style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
          ),
        ),
      ),
    );
  }

  Widget _buildOrderSummary(OrderData order, BuildContext context) {
    final productQty = order.orderDetails.fold(0, (sum, item) => sum + item.productQty);
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      // mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        _buildCustomRowSection(label: 'สินค้ารวม ', value: '$productQty รายการ'),
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            const CustomText(text: 'ยอดเงินรวม : ', fontSize: 14, color: Colors.black87, fontWeight: FontWeight.w600),
            CustomText(text: '฿${FormatNumbers().format((order.totalAmount).toString())}', fontSize: 15, color: Colors.blue.shade600, fontWeight: FontWeight.bold),
          ],
        ),
      ],
    );
  }

  void _navigateToProductDetail(String slug, String? firstThumbnail) async {
    if (slug.trim().isEmpty) return;

    // context.read<ProductDetailCubit>().onGetProductDetail(slug: slug, context: context);
    // context.read<ProductBloc>().add(GetProductByOrderDetail(
    // masterOrerId: orderItem.masterOrderId!.toInt(), productId: orderItem.orderDetails[indexdetail].productId!.toInt(), orderId: orderItem.id!.toInt()));
    context.read<CartBloc>().add(GetCartEvent());
    // await Navigator.pushNamed(context, AppRoutes.productDetailPage, arguments: slug);
    await Navigator.push(
      context,
      PageTransition(
        type: PageTransitionType.rightToLeft,
        isIos: true,
        duration: const Duration(milliseconds: 100),
        reverseDuration: const Duration(milliseconds: 100),
        child: ProductDetailPage(scrollController: ScrollController(), slug: slug, firstThumbnail: firstThumbnail ?? ''),
      ),
    );
  }

  Widget _buildCustomRowSection({required String label, String? value, double? valueFontSize, Color? valueColor}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        CustomText(
          text: '$label : ',
          fontSize: 13,
          fontWeight: FontWeight.w600,
          // color: Colors.black54,
        ),
        CustomText(text: value ?? '', fontSize: valueFontSize ?? 13, color: valueColor ?? Colors.black87, fontWeight: FontWeight.w600),
      ],
    );
  }

  Widget _shopSection(BuildContext context, ShopDetailNew? shop, OrderData orderItem) {
    final status = orderItem.status;
    final statusColor = status == 6 ? Colors.red : FlutterFlowTheme.of(context).primaryColor;

    if (shop == null) return const SizedBox.shrink();

    return GestureDetector(
      onTap: () {
        final ShopTabBloc tabBloc = BlocProvider.of<ShopTabBloc>(context);
        tabBloc.add(ShopTabEvent.tab1);

        context.read<ShopBloc>().add(GetShopEvent(shop.id.toString()));
        Navigator.pushNamed(context, AppRoutes.shopScreen, arguments: shop.id.toString());
      },
      child: Row(
        children: [
          CustomImageNetwork(url: shop.logo, size: 30, fit: BoxFit.cover, borderRadius: BorderRadius.circular(8)),
          const SizedBox(width: 10),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Flexible(
                        child: CustomText(text: shop.name, fontSize: 14, color: Colors.black87, fontWeight: FontWeight.bold, maxLines: 1, overflow: TextOverflow.ellipsis),
                      ),
                      const SizedBox(width: 5),
                      const Icon(FontAwesomeIcons.chevronRight, size: 12, color: Colors.black54),
                    ],
                  ),
                ),
                Flexible(
                  child: CustomText(
                    text: orderItem.orderStatus?.name ?? '',
                    fontSize: 13,
                    color: statusColor,
                    fontWeight: FontWeight.bold,
                    maxLines: 1,
                    textAlign: TextAlign.end,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _onViewDetail(OrderData orderItem, bool cancel) async {
    context.read<OrderBloc>().add(GetOrderDetailEvent(orderItem.id.toString()));
    // await Future.delayed(const Duration(milliseconds: 500));
    await Navigator.pushNamed(context, AppRoutes.orderWidget, arguments: {'cancel': cancel, 'status': widget.status.toString()});
    context.read<OrderBloc>().add(GetOrderEvent(offset: '0', status: widget.status));
  }
}

class _MyDialog extends StatefulWidget {
  _MyDialog({this.reasons, this.onSelectedReasonsListChanged});

  final reasons;
  var onSelectedReasonsListChanged;

  @override
  _MyDialogState createState() => _MyDialogState();
}

class _MyDialogState extends State<_MyDialog> {
  int? value;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        height: 500,
        child: Column(
          children: <Widget>[
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [Text('กรุณาเลือกเหตุผลในการยกเลิก', style: FlutterFlowTheme.of(context).subtitle1)],
                  ),
                ),
                Divider(thickness: 2, indent: 0, endIndent: 0, color: FlutterFlowTheme.of(context).primaryBackground),
              ],
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      '*** กรุณาเลือกเหตุผลที่คุณต้องการยกเลิกคำสั่งซื้อ สินค้าทุกชิ้นในคำสั่งซื้อนี้จะถูกยกเลิก คุณจะไม่สามารถแก้ไขคำขอยกเลิกได้หลังกดยืนยันแล้ว',
                      style: FlutterFlowTheme.of(context).bodyText1,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView.builder(
                itemCount: widget.reasons!.length,
                itemBuilder: (BuildContext context, int index) {
                  final reasonDetail = widget.reasons![index]['details'];
                  return Container(
                    child: RadioListTile(
                      title: Text(
                        reasonDetail,
                        style: FlutterFlowTheme.of(
                          context,
                        ).title2.override(fontFamily: 'Sarabun', color: Colors.black, fontSize: 14, fontWeight: FontWeight.bold, useGoogleFonts: false),
                      ),
                      value: index,
                      selected: value == index,
                      controlAffinity: ListTileControlAffinity.leading,
                      onChanged: (ind) {
                        value = ind;
                        setState(() {});
                      },
                      groupValue: value,
                    ),
                  );
                },
              ),
            ),
            Divider(thickness: 2, indent: 0, endIndent: 0, color: FlutterFlowTheme.of(context).primaryBackground),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                  onTap: () async {
                    Navigator.pop(context);
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 50,
                    margin: const EdgeInsets.fromLTRB(0, 8, 16, 16),
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        fit: BoxFit.cover,
                        image: Image.asset(
                          // 'assets/images/button_disable_bg.png',
                          UImageAssets.BUTTON_DISABLE_BG,
                        ).image,
                      ),
                      borderRadius: BorderRadius.circular(14),
                    ),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                      child: Text(
                        'ไม่ใช่ตอนนี้',
                        textAlign: TextAlign.center,
                        style: FlutterFlowTheme.of(
                          context,
                        ).title2.override(fontFamily: 'Sarabun', color: Colors.black, fontSize: 15, fontWeight: FontWeight.bold, useGoogleFonts: false),
                      ),
                    ),
                  ),
                ),
                InkWell(
                  onTap: () async {
                    if (value != null) {
                      setState(() {
                        print('cancel text');
                        widget.onSelectedReasonsListChanged(widget.reasons![value]['id'] as int?);
                        Navigator.pop(context);
                      });
                    }
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 50,
                    margin: const EdgeInsets.fromLTRB(0, 8, 8, 16),
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        fit: BoxFit.cover,
                        image: Image.asset(
                          // 'assets/images/button_bg.png',
                          UImageAssets.BUTTON_BG,
                        ).image,
                      ),
                      borderRadius: BorderRadius.circular(14),
                    ),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                      child: Text(
                        'ยกเลิกคำสั่งซื้อ',
                        textAlign: TextAlign.center,
                        style: FlutterFlowTheme.of(context).title2.copyWith(color: Colors.white, fontSize: 15, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
