import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop_chill_app/app_routers.dart';
import 'package:shop_chill_app/screens/myorder/widgets/my_order_item_widget.dart';
import 'package:shop_chill_app/shered/widgets/simmer_loading/basic_simmer_loading.dart';
import 'package:shop_chill_app/screens/order/bloc/order_bloc.dart';
import 'package:shop_chill_app/screens/order/model/order_model.dart';
import 'package:shop_chill_app/shered/widgets/back_button.dart';
import 'package:shop_chill_app/shered/widgets/placeholder_message_widget.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import 'package:flutter/material.dart';

class MyOrderPage extends StatefulWidget {
  final int? selectIndex;

  const MyOrderPage({super.key, this.selectIndex});

  @override
  _MyOrderPageState createState() => _MyOrderPageState();
}

class _MyOrderPageState extends State<MyOrderPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ValueNotifier<String> statusNotifier = ValueNotifier<String>('');

  /*   static const List<OrderTab> tabs = [
    OrderTab(title: 'ทั้งหมด', status: ''),
    OrderTab(title: 'ที่ต้องชำระ', status: '1,17'),
    OrderTab(title: 'ที่ต้องจัดส่ง', status: '3,2'),
    OrderTab(title: 'ที่ต้องได้รับ', status: '4'),
    OrderTab(title: 'สำเร็จแล้ว', status: '5'),
    OrderTab(title: 'ยกเลิกแล้ว', status: '6'),
    OrderTab(title: 'คืนเงิน/คืนสินค้า', status: '18'),
  ]; */
  static const List<OrderTab> tabs = [
    OrderTab(title: 'ทั้งหมด', status: ''), // ไม่กรองสถานะ
    OrderTab(title: 'ที่ต้องชำระ', status: '1'),
    OrderTab(title: 'ที่ต้องจัดส่ง', status: '2'),
    OrderTab(title: 'ที่ต้องได้รับ', status: '4'), // processing
    OrderTab(title: 'สำเร็จแล้ว', status: '5'), // delivered
    OrderTab(title: 'ยกเลิกแล้ว', status: '6'), // cancelled
    OrderTab(title: 'คืนเงิน/คืนสินค้า', status: '7'),
  ];

  @override
  void initState() {
    super.initState();

    _tabController = TabController(vsync: this, length: tabs.length, initialIndex: widget.selectIndex ?? 0);
    _tabController.addListener(() => _updateStatus(_tabController.index));
    _updateStatus(widget.selectIndex ?? 0);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(appBar: _buildAppBar(context), backgroundColor: Colors.grey.shade100, body: _buildBody(context));
  }

  Widget _buildBody(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Column(
        children: [
          _buildTabBar(context),
          BlocBuilder<OrderBloc, OrderState>(
            buildWhen: (previous, current) {
              if (previous is OrderLoaded && current is OrderLoaded) {
                return previous.orderModel.masterOrders != current.orderModel.masterOrders;
              }
              return true;
            },
            builder: (context, state) {
              if (state is OrderLoading) return const BasicSimmerLoading();

              if (state is OrderLoaded) {
                final item = state.orderModel.masterOrders;
                return _buildOrderList(item, state);
              }
              return const PlaceholderMessageWidget(message: 'มีบางอย่างผิดพลาด!');
            },
          ),
        ],
      ),
    );
  }

  Widget _buildOrderList(List<MasterOrders> masterOrders, OrderLoaded state) {
    return Expanded(
      child: ValueListenableBuilder<String>(
        valueListenable: statusNotifier,
        builder: (context, status, _) {
          return TabBarView(
            controller: _tabController,
            physics: const ClampingScrollPhysics(),
            children: tabs.map((tab) => MyOrderItemWidget(masterOrders: masterOrders, status: status, state: state)).toList(),
          );
        },
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: FlutterFlowTheme.of(context).primaryColor,
        splashBorderRadius: BorderRadius.circular(12),
        unselectedLabelColor: FlutterFlowTheme.of(context).secondaryText,
        labelStyle: FlutterFlowTheme.of(context).subtitle1.copyWith(fontWeight: FontWeight.bold, fontSize: 14),
        indicatorSize: TabBarIndicatorSize.label,
        indicatorWeight: 2,
        indicatorColor: FlutterFlowTheme.of(context).primaryColor,
        tabs: tabs.map((tab) => Tab(text: tab.title, height: 30)).toList(),
      ),
    );
  }

  void _updateStatus(int index) {
    final String newStatus = tabs[index].status;
    if (statusNotifier.value != newStatus) {
      statusNotifier.value = newStatus;
      print("✅ อัพเดท status => ${statusNotifier.value}");
      context.read<OrderBloc>().add(ClearOrderEvent());
      context.read<OrderBloc>().add(OnchangePageEvent(status: newStatus));
    }
  }

  AppBar? _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      automaticallyImplyLeading: false,
      leading: ShopChillBackButton(
        onBack: () async {
          Navigator.of(context).popUntil((route) => route.settings.name == AppRoutes.navBar || route.settings.name == AppRoutes.home);
        },
      ),
      title: Text(
        'รายการคำสั่งซื้อ',
        style: FlutterFlowTheme.of(context).title3.copyWith(color: Colors.black, fontWeight: FontWeight.bold, fontSize: 18),
      ),
      centerTitle: false,
      elevation: 0,
    );
  }
}

class OrderTab {
  final String title;
  final String status;

  const OrderTab({required this.title, required this.status});
}
