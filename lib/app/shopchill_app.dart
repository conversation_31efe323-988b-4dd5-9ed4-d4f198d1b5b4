import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:shop_chill_app/blocs/bloc_providers.dart';
import 'package:shop_chill_app/config/theme_config.dart';
import 'package:shop_chill_app/sockets/socket_manager.dart';
import 'package:shop_chill_app/app/splash_screen.dart';

import '../app_routers.dart';
import '../config/shopchill_loading/shopchill_loading.dart';
import '../screens/flutter_flow/internationalization.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class ShopchillApp extends StatefulWidget {
  const ShopchillApp({super.key});
  @override
  State<ShopchillApp> createState() => _ShopchillAppState();

  static _ShopchillAppState of(BuildContext context) => context.findAncestorStateOfType<_ShopchillAppState>()!;
}

class _ShopchillAppState extends State<ShopchillApp> {
  Locale? _locale;
  ThemeMode _themeMode = ThemeMode.system;

  final SocketManager _socketManager = SocketManager();
  final RouteObserverService routeObserver = RouteObserverService();


  @override
  void initState() {
    super.initState();
    _socketManager.initSocket();
    _socketManager.initSocketRoom();
  }

  void setLocale(String language) => setState(() => _locale = createLocale(language));

  void setThemeMode(ThemeMode mode) => setState(() => _themeMode = mode);

  @override
  void dispose() {
    // Dispose of the sockets properly
    _socketManager.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentRoute = ModalRoute.of(context)?.settings.name;
    print('Current route: $currentRoute');
    return MultiBlocProvider(
      providers: BlocProviderManager.createBlocProviders(context),
      child: MediaQuery(
        data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1.0)),
        child: MaterialApp(
          navigatorObservers: [routeObserver],
          title: 'ShopChill',
          navigatorKey: navigatorKey,
          builder: ShopChillLoading.builder,
          debugShowCheckedModeBanner: false,
          localizationsDelegates: const [
            FFLocalizationsDelegate(),
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          locale: _locale,
          supportedLocales: const [Locale('th')],
          theme: ThemeConfig.getThemeData(isDarkMode: _themeMode == ThemeMode.dark),
          themeMode: _themeMode,
          initialRoute: AppRoutes.home,
          onGenerateRoute: AppRouteGenerator.generateRoute,
          home: const SplashScreen(),
        ),
      ),
    );
  }
}


class RouteObserverService extends NavigatorObserver {
  String? currentRouteName;

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    currentRouteName = route.settings.name;
    print('➡️ Pushed to: $currentRouteName');
    super.didPush(route, previousRoute);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    currentRouteName = previousRoute?.settings.name;
    print('⬅️ Popped to: $currentRouteName');
    super.didPop(route, previousRoute);
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    currentRouteName = newRoute?.settings.name;
    print('🔁 Replaced with: $currentRouteName');
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
  }
}
