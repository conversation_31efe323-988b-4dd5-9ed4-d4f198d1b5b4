import 'package:flutter/material.dart';
import 'package:chewie/chewie.dart';
import 'package:shop_chill_app/screens/nav_bar/nav_bar_page.dart';
import 'package:shop_chill_app/shered/device/app_info_service.dart';
import 'package:video_player/video_player.dart';
import 'package:shop_chill_app/app_state.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:shop_chill_app/backend/api_requests/domain.dart';
import 'package:shop_chill_app/backend/schema/local_database/shopchill_database.dart';
import 'package:shop_chill_app/config/app_links_deep_link.dart';
import 'package:shop_chill_app/config/app_update_config_service.dart';
import 'package:shop_chill_app/firebase/firebase_options.dart';
import 'package:shop_chill_app/screens/notification/notification_service.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  late VideoPlayerController _videoController;
  ChewieController? _chewieController;
  bool _isInitialized = false;

  bool _showMain = false;
  final _videoPathV1 = 'assets/videos/shopchill_splash_v1.mp4';
  final _videoPathV2 = 'assets/videos/shopchill_splash_v2.mp4';

  @override
  void initState() {
    super.initState();
    _setupSplash();
  }

  Future<void> _setupSplash() async {
    await Future.wait([_initializeVideoWithFallback(), _initializeApp()]);
  }

  Future<void> _initializeVideoWithFallback() async {
    try {
      final v1Loaded = await _tryLoadVideo(_videoPathV1);
      if (!v1Loaded) await _tryLoadVideo(_videoPathV2);
      return;
    } catch (e) {
      debugPrint("Video load failed for all paths: $e");
      _showMainScreen();
    }
  }

  Future<bool> _tryLoadVideo(String path) async {
    try {
      _videoController = VideoPlayerController.asset(path);
      await _videoController.initialize();

      _chewieController = ChewieController(
        videoPlayerController: _videoController,
        autoPlay: true,
        looping: false,
        showControls: false,
        allowMuting: false,
        allowPlaybackSpeedChanging: false,
      );

      _videoController.addListener(() {
        if (!_showMain && _videoController.value.position >= _videoController.value.duration - const Duration(milliseconds: 1000)) {
          _showMainScreen();
        }
      });

      if (mounted) setState(() => _isInitialized = true);

      return true;
    } catch (e) {
      debugPrint("Video load failed for $path: $e");
      return false;
    }
  }

  Future<void> _initializeApp() async {
    try {
      await domain.loadEnvFromPrefs();
      await dotenv.load(fileName: ".env");
      await AppInfoService.initialize();
      if (Firebase.apps.isEmpty) {
        await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
      }

      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
      await FirebaseMessaging.instance.setAutoInitEnabled(true);
      await AppUpdateConfigService.initialize();
      ShopChillDatabase().iSarService();
      FFAppState();
      NotificationService().initialState();
      await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);

      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await AppLinksDeepLink().initDeepLinks();
      });
    } catch (e) {
      debugPrint("Error during app init: $e");
    }
  }

  void _showMainScreen() {
    if (!mounted) return;
    setState(() {
      _showMain = true;
    });
  }

  @override
  void dispose() {
    _chewieController?.dispose();
    _videoController.dispose();
    super.dispose();
  }

  Widget _buildVideoOrFallback() {
    if (_isInitialized && (_chewieController?.videoPlayerController.value.isInitialized ?? false)) {
      return SizedBox.expand(
        child: FittedBox(
          fit: BoxFit.cover,
          child: SizedBox(
            width: _videoController.value.size.width,
            height: _videoController.value.size.height,
            child: Chewie(controller: _chewieController!),
          ),
        ),
      );
    } else {
      // fallback image
      return SizedBox.expand(child: Image.asset('assets/images/splash_screen_v2.png', fit: BoxFit.cover));
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_showMain) {
      return const NavBarPage(initialPage: 0);
    }
    return Scaffold(body: _buildVideoOrFallback());
  }
}
