import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:page_transition/page_transition.dart';
import 'package:shop_chill_app/screens/affiliate/models/billings_response.dart';
import 'package:shop_chill_app/screens/affiliate/models/orders_affiliate_response.dart' show Order;
import 'package:shop_chill_app/screens/affiliate/models/social_response.dart';
import 'package:shop_chill_app/screens/affiliate/nav_bar_affiliate_screen.dart';
import 'package:shop_chill_app/screens/affiliate/screens/bank_registration/bank_registration_affiliate_status_screen.dart';
import 'package:shop_chill_app/screens/affiliate/screens/bank_registration/bank_registration_screen.dart';
import 'package:shop_chill_app/screens/affiliate/screens/bank_registration/address_selection_screen.dart';
import 'package:shop_chill_app/screens/affiliate/screens/custom_the_link/custom_the_link_screen.dart';
import 'package:shop_chill_app/screens/affiliate/screens/dashboard/dashboard_screen.dart';
import 'package:shop_chill_app/screens/affiliate/screens/orders/order_detail_screen.dart';
import 'package:shop_chill_app/screens/affiliate/screens/orders/search_order_screen.dart';
import 'package:shop_chill_app/screens/affiliate/screens/product_affiliate/product_details_affiliate.dart';
import 'package:shop_chill_app/screens/affiliate/screens/profile_affiliate/categories_affiliate_screen.dart';
import 'package:shop_chill_app/screens/affiliate/screens/profile_affiliate/setting_profile_affiliate_screen.dart';
import 'package:shop_chill_app/screens/affiliate/screens/register_affiliate/affiliate_status_screen.dart';
import 'package:shop_chill_app/screens/affiliate/screens/register_affiliate/register_affiliate_screen.dart';
import 'package:shop_chill_app/screens/affiliate/screens/register_affiliate/verify_email_screen.dart';
import 'package:shop_chill_app/screens/affiliate/screens/revenue/revenue_detail_screen.dart';
import 'package:shop_chill_app/screens/affiliate/screens/shop_detail_affiliate/affiliate_shop_detail_screen.dart';
import 'package:shop_chill_app/screens/affiliate/screens/social_platform_screen/add_social_platform_screen.dart';
import 'package:shop_chill_app/screens/affiliate/screens/social_platform_screen/social_platform_screen.dart';
import 'package:shop_chill_app/screens/chat/new_chat_screen.dart';
import 'package:shop_chill_app/screens/checkout/widgets/select_system_coupon.dart';
import 'package:shop_chill_app/screens/edit_address/models/standard_address_model.dart';
import 'package:shop_chill_app/screens/edit_address/pages/address_selection_page/address_selection_page.dart';
import 'package:shop_chill_app/screens/home/<USER>/1get1/1Get1Screen.dart';
import 'package:shop_chill_app/screens/home/<USER>/code_discount/code_discount.dart';
import 'package:shop_chill_app/screens/home/<USER>/deal100/deal100_screen.dart';
import 'package:shop_chill_app/screens/home/<USER>/flash_sale/flash_sale_screen.dart';
import 'package:shop_chill_app/screens/home/<USER>/hot_deal/hot_deal.dart';
import 'package:shop_chill_app/screens/home/<USER>/mutelu/mutelu_page.dart';
import 'package:shop_chill_app/screens/home/<USER>/coupon_shippng/earn_coupon_page.dart';
import 'package:shop_chill_app/screens/login/login_widget.dart';
import 'package:shop_chill_app/screens/mall/mall_widget.dart';
import 'package:shop_chill_app/screens/my_cart/models/cart_model.dart';
import 'package:shop_chill_app/screens/my_cart/new_my_cart_screen.dart';
import 'package:shop_chill_app/screens/my_coupon/my_coupon_page.dart';
import 'package:shop_chill_app/screens/myorder/my_order_page.dart';
import 'package:shop_chill_app/screens/myorder/widgets/order_tracking_widget.dart';
import 'package:shop_chill_app/screens/nav_bar/nav_bar_page.dart';
import 'package:shop_chill_app/screens/order/model/order_detail_new_model.dart';
import 'package:shop_chill_app/screens/order/order_widget.dart';
import 'package:shop_chill_app/screens/orderdetail/orderdetail_widget.dart';
import 'package:shop_chill_app/screens/payment/payment_screen.dart';
import 'package:shop_chill_app/screens/product_page/product_detail_page.dart';
import 'package:shop_chill_app/screens/refund/models/refund_model.dart';
import 'package:shop_chill_app/screens/refund/pages/refund_cf_product_page.dart';
import 'package:shop_chill_app/screens/refund/pages/refund_reject_page.dart';
import 'package:shop_chill_app/screens/refund/pages/refund_return_parcel_page.dart';
import 'package:shop_chill_app/screens/refund/pages/refund_update_payment_page.dart';
import 'package:shop_chill_app/screens/refund/refund_screen.dart';
import 'package:shop_chill_app/screens/reviews/reviews_widget.dart';
import 'package:shop_chill_app/screens/search/widgets/shopchill_search_auto_complete.dart';
import 'package:shop_chill_app/screens/search/widgets/shopchill_search_suggestion_shops.dart';
import 'package:shop_chill_app/screens/setting/pages/affiliate_term_of_service_page.dart';
import 'package:shop_chill_app/screens/shop/shop_screen.dart';
import 'package:shop_chill_app/shered/widgets/image/photo_full_screen_basic.dart';
import 'package:shop_chill_app/app/splash_screen.dart';

import 'screens/home/<USER>';
import 'screens/home/<USER>/all_menu/all_menu_page.dart';
import 'screens/payment_complete/payment_complete_widget.dart';
import 'screens/product_page/model/product_detail_model.dart' as PDD;
import 'screens/product_page/model/product_model.dart';
import 'shered/util/product_list_display.dart';

class AppRoutes {
  static const String navBar = '/nav-bar';
  static const String home = '/';
  static const String splashScreen = '/splash-screen';
  static const String login = '/login';
  static const String myCart = '/my-cart';
  static const String myOrder = '/my-order';
  static const String orderWidget = '/my-widget';
  static const String productPage = '/product-page';
  static const String earnCouponScreen = '/earn-coupon-screen';
  static const String codeDiscountScreen = '/code-discount-screen';
  static const String hotDeal = '/hot-deal';
  static const String deal100 = '/deal-100';
  static const String getOneGet1 = '/get-one-get-1';
  static const String muteluScreen = '/mutelu';
  static const String mallScreen = '/mall-screen';
  static const String shopScreen = '/shop-screen';
  static const String shopChillSearchAutoComplete = '/shop-chill-search';
  static const String selectShopCoupon = '/select-shop-coupon';
  static const String selectSystemCouponScreen = '/select-system-coupon-screen';
  static const String reviewWidget = '/review-widget';
  static const String productImagePreview = '/product-image-preview';
  static const String productListDisplay = '/product-list-display';
  static const String oneGetOne = '/one-get-one';
  static const String paymentComplete = '/payment-complete';
  static const String paymentScreen = '/payment-screen';
  static const String paymentMethod = '/payment-method';
  static const String orderDetail = '/order-detail';
  static const String chatWidget = '/chat-widget';
  static const String flashSaleScreen = '/flash-sale-screen';
  // static const String liveScreen = '/live-screen';
  static const String affiliateScreen = '/affiliate-screen';
  static const String affiliateDashboardScreen = '/affiliate-dashboard-screen';
  static const String myCouponScreen = '/my-coupon-screen';
  static const String registerAffiliateScreen = '/register-affiliate-screen';
  static const String affiliateStatusScreen = '/affiliate-status-screen';
  static const String categoriesAffiliateScreen = '/categories-affiliate-screen';
  static const String verifyEmailScreen = '/verify-email-screen';
  static const String settingProfileAffiliateScreen = '/setting-profile-affiliate-screen';
  static const String bankRegistrationScreen = '/bank-registration-screen';
  static const String affiliateAddressSelectionScreen = '/affiliate-address-selection-screen';
  static const String bankRegistrationAffiliateStatusScreen = '/bank-registration-affiliate-status-screen';
  static const String orderDetailScreen = '/order-detail-screen';
  static const String searchOrderScreen = '/search-order-screen';
  static const String productDetailPage = '/product-detail-page';
  static const String shopChillSearchSuggestionShops = '/shopchill-search-suggestion-shops';

  static const String allMenuPage = '/all-menu-page';

  static const String orderTracking = '/order-tracking';

  static const String refundScreen = '/refund-screen';
  static const String refundUpdatePayment = '/refund-update-payment';
  static const String refundReturnParcelPage = '/refund-return-parcel-page';
  static const String refundCfProductPage = '/refund-cf-product-page';
  static const String refundRejectPage = '/refund-reject-page';
  static const String affiliateProductDetailScreen = '/affiliate-product-detail-screen';
  static const String affiliateShopDetailScreen = '/affiliate-shop-detail-screen';
  static const String revenueDetailScreen = '/revenue-detail-screen';
  static const String socialPlatformScreen = '/social-platform-screen';
  static const String addSocialPlatformScreen = '/add-social-platform-screen';
  static const String affiliateCustomTheLinkScreenPage = '/affiliate-custom-the-link-screen-page';
  static const String affiliateTermOfServicePage = '/affiliate-term-of-service-page';
  static const String addressSelectionPage = '/address-selection-page';
}

class AppRouteGenerator {
  // You can inject your BLoCs here if needed

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRoutes.home:
        return CupertinoPageRoute(builder: (_) => const HomeScreen());

      case AppRoutes.splashScreen:
        return CupertinoPageRoute(builder: (_) => const SplashScreen());

      case AppRoutes.login:
        return CupertinoPageRoute(builder: (_) => const LoginWidget());

      case AppRoutes.navBar:
        return CupertinoPageRoute(builder: (_) => NavBarPage(initialPage: (settings.arguments ?? 0) as int));

      case AppRoutes.myCart:
        final arguments = settings.arguments as List<String>?;
        return CupertinoPageRoute(builder: (_) => NewMyCartScreen(orderIds: arguments ?? []));

      /*case AppRoutes.productPage:
        return CupertinoPageRoute(
            builder: (_) => ProductPageWidget(scrollController: ScrollController()));*/

      case AppRoutes.productDetailPage:
        final slug = settings.arguments as String;

        return PageTransition(
          type: PageTransitionType.rightToLeft,
          isIos: true,
          duration: const Duration(milliseconds: 100),
          reverseDuration: const Duration(milliseconds: 100),
          child: ProductDetailPage(scrollController: ScrollController(), slug: slug, firstThumbnail: null),
        );
      /* case AppRoutes.productDetailPage:
        return CupertinoPageRoute(
          builder: (_) {
            final slug = settings.arguments as String;
            return ProductDetailPage(scrollController: ScrollController(), slug: slug);
          },
        ); */

      case AppRoutes.earnCouponScreen:
        return CupertinoPageRoute(builder: (_) => const EarnCouponScreen());

      case AppRoutes.codeDiscountScreen:
        return CupertinoPageRoute(builder: (_) => const CodeDiscountScreen());

      case AppRoutes.hotDeal:
        return CupertinoPageRoute(builder: (_) => HotDeal(title: settings.arguments as String));

      case AppRoutes.deal100:
        return CupertinoPageRoute(builder: (_) => const Deal100Screen());

      case AppRoutes.getOneGet1:
        return CupertinoPageRoute(builder: (_) => const OneGet1Screen());

      //
      case AppRoutes.allMenuPage:
        return CupertinoPageRoute(
          builder: (_) {
            final List<Map<String, String>> homeMenuMobile = settings.arguments as List<Map<String, String>>;
            return AllMenuPage(homeMenuMobile: homeMenuMobile);
          },
        );

      case AppRoutes.muteluScreen:
        return CupertinoPageRoute(builder: (_) => MuteluPage(title: settings.arguments as String));

      case AppRoutes.mallScreen:
        return CupertinoPageRoute(builder: (_) => const MallWidget());

      case AppRoutes.shopScreen:
        final String? shopId = settings.arguments as String?;
        return PageTransition(
          type: PageTransitionType.rightToLeft,
          isIos: true,
          duration: const Duration(milliseconds: 100),
          reverseDuration: const Duration(milliseconds: 100),
          child: ShopScreen(shopId: shopId ?? ''),
        );
      /*  case AppRoutes.shopScreen:
        final String? shopId = settings.arguments as String?;
        return CupertinoPageRoute(builder: (_) => ShopScreen(shopId: shopId ?? '')); */

      case AppRoutes.shopChillSearchAutoComplete:
        final arguments = settings.arguments as Map<String, dynamic>? ?? {};
        final bool argsfromAffiliate = arguments['fromAffiliate'] as bool? ?? false;

        return CupertinoPageRoute(builder: (_) => ShopChillSearchAutoComplete(fromAffiliate: argsfromAffiliate));

      case AppRoutes.myOrder:
        return MaterialPageRoute(builder: (_) => const MyOrderPage());

      case AppRoutes.orderWidget:
        if (settings.arguments != null) {
          final arguments = settings.arguments as Map<String, dynamic>;

          final isForceCancel = arguments['cancel'] as bool? ?? false;
          final status = arguments['status'] as String? ?? '';
          final isForceReturnParcel = arguments['isForceReturnParcel'] as bool? ?? false;

          return CupertinoPageRoute(
            builder: (_) => OrderWidget(isForceCancel: isForceCancel, status: status, isForceReturnParcel: isForceReturnParcel),
          );
        } else {
          return CupertinoPageRoute(builder: (_) => const OrderWidget(isForceCancel: false, status: ''));
        }

      case AppRoutes.refundReturnParcelPage:
        return CupertinoPageRoute(builder: (_) => RefundReturnParcelPage(orderId: settings.arguments as String));

      case AppRoutes.refundCfProductPage:
        return CupertinoPageRoute(builder: (_) => RefundCfProductPage(orderId: settings.arguments as String));

      case AppRoutes.refundRejectPage:
        final Map<String, dynamic> arguments = settings.arguments as Map<String, dynamic>;

        final RefundStatus status = arguments['status'] as RefundStatus;
        final RefundModel refund = arguments['refund'] as RefundModel;
        final String returnTotalAmount = arguments['returnTotalAmount'] as String;

        return CupertinoPageRoute(
          builder: (_) => RefundRejectPage(rejectStatus: status, refund: refund, returnTotalAmount: returnTotalAmount),
        );

      case AppRoutes.orderTracking:
        return CupertinoPageRoute(builder: (_) => OrderTrackingWidget(order: settings.arguments as OrderDetailNewModelData));

      case AppRoutes.refundScreen:
        final arguments = settings.arguments as Map<String, dynamic>;
        final orderId = arguments['orderId'] as String;
        final status = arguments['status'] as String;
        final isLoadFullOption = arguments['isLoadFullOption'] as bool? ?? false;
        return CupertinoPageRoute(
          builder: (_) => RefundScreen(orderId: orderId, status: status, isLoadFullOption: isLoadFullOption),
        );

      case AppRoutes.refundUpdatePayment:
        final arguments = settings.arguments as Map<String, dynamic>;
        final orderId = arguments['orderId'] as String;
        final status = arguments['status'] as String;
        return CupertinoPageRoute(
          builder: (_) => RefundUpdatePaymentPage(orderId: orderId, status: status),
        );

      /*  case AppRoutes.selectShopCoupon:
        return CupertinoPageRoute(builder: (_) {
          final Map<String, dynamic> arguments = settings.arguments as Map<String, dynamic>;
          final List<CouponModel> shopCoupon = arguments['shopCoupon'] as List<CouponModel>;
          final List<int> shopId = arguments['shopId'] as List<int>;
          final List<Cart> selectedCartList = arguments['selectedCartList'] as List<Cart>;
          final List<Products> selectedProductList = arguments['selectedProductList'] as List<Products>;

          return SelectShopCoupon(
            shopCoupon: shopCoupon,
            shopId: shopId,
            selectedCartList: selectedCartList,
            selectedProductList: selectedProductList,
          );
        });
 */
      case AppRoutes.selectSystemCouponScreen:
        return CupertinoPageRoute(
          builder: (_) {
            final Map<String, dynamic> arguments = settings.arguments as Map<String, dynamic>;
            final List<Cart> selectedCartList = arguments['selectedCartList'] as List<Cart>;
            final List<Products> selectedProductList = arguments['selectedProductList'] as List<Products>;
            final double totalAmount = arguments['totalAmount'] as double;

            return SelectSystemCouponScreen(selectedCartList: selectedCartList, selectedProductList: selectedProductList, totalAmount: totalAmount);
          },
        );

      case AppRoutes.reviewWidget:
        return CupertinoPageRoute(builder: (_) => ReviewsWidget(product: settings.arguments as PDD.ProductDetailModel));

      case AppRoutes.oneGetOne:
        return CupertinoPageRoute(builder: (_) => ProductListDisplay(products: settings.arguments as List<Products>));

      case AppRoutes.paymentScreen:
        final Map<String, dynamic> arguments = settings.arguments as Map<String, dynamic>;
        final String masterOrderId = arguments['masterOrderId'] as String;
        final String paymentId = arguments['paymentId'] as String;
        final String gatewayId = arguments['gatewayId'] as String;
        final String txn = arguments['txn'] as String;
        final String pid = arguments['pid'] as String;
        final String goToRouterName = arguments['goToRouterName'] as String;
        final String? orderId = arguments['orderId'] as String?;
        final String selectIndex = arguments['selectIndex'] as String;

        return MaterialPageRoute(
          builder: (_) => PaymentScreen(
            masterOrderId: masterOrderId,
            paymentId: paymentId,
            gatewayId: gatewayId,
            txn: txn,
            pid: pid,
            goToRouterName: goToRouterName,
            orderId: orderId ?? '',
            selectIndex: selectIndex.isNotEmpty ? selectIndex : '0',
          ),
        );

      /* case AppRoutes.paymentMethod:
        return CupertinoPageRoute(
          builder: (_) {
            final paymentGateways = settings.arguments as List<PaymentGateways>;

            return PaymentmethodWidget(paymentGateways: paymentGateways);
          },
        ); */

      case AppRoutes.chatWidget:
        return CupertinoPageRoute(builder: (_) => const ChatWidget());

      case AppRoutes.flashSaleScreen:
        return CupertinoPageRoute(builder: (_) => const FlashSaleScreen());
      case AppRoutes.bankRegistrationScreen:
        return CupertinoPageRoute(
          builder: (_) {
            final arguments = settings.arguments as Map<String, dynamic>? ?? {};
            return BankRegistrationScreen(isEdit: arguments['isEdit'] ?? false);
          },
        );

      // case AppRoutes.liveScreen:
      //   return CupertinoPageRoute(builder: (_) => LiveWidget(onBack: settings.arguments as bool));

      case AppRoutes.affiliateScreen:
        return CupertinoPageRoute(builder: (_) => NavBarAffiliateScreen());

      case AppRoutes.affiliateDashboardScreen:
        return CupertinoPageRoute(builder: (_) => const DashboardScreen());
      case AppRoutes.registerAffiliateScreen:
        return CupertinoPageRoute(builder: (_) => const RegisterAffiliateScreen());
      case AppRoutes.settingProfileAffiliateScreen:
        return CupertinoPageRoute(builder: (_) => const SettingProfileAffiliateScreen());
      case AppRoutes.affiliateStatusScreen:
        return CupertinoPageRoute(
          builder: (_) {
            final arguments = settings.arguments as Map<String, dynamic>? ?? {};
            return AffiliateStatusScreen(status: arguments['status'] ?? 0, email: arguments['email'] ?? '');
          },
        );
      case AppRoutes.categoriesAffiliateScreen:
        return CupertinoPageRoute(builder: (_) => const CategoriesAffiliateScreen());
      case AppRoutes.affiliateCustomTheLinkScreenPage:
        return CupertinoPageRoute(builder: (_) => const CustomTheLinkScreen());
      case AppRoutes.verifyEmailScreen:
        return CupertinoPageRoute(builder: (_) => const VerifyEmailScreen());
      case AppRoutes.affiliateAddressSelectionScreen:
        return CupertinoPageRoute(builder: (_) => const AffiliateAddressSelectionScreen());
      case AppRoutes.bankRegistrationAffiliateStatusScreen:
        final arguments = settings.arguments as Map<String, dynamic>? ?? {};
        return PageTransition(
          type: PageTransitionType.fade,
          duration: const Duration(milliseconds: 0),
          reverseDuration: const Duration(milliseconds: 0),
          child: BankRegistrationAffiliateStatusScreen(status: arguments['status'] ?? 0),
        );
      case AppRoutes.orderDetailScreen:
        final arguments = settings.arguments as Map<String, dynamic>? ?? {};
        return PageTransition(
          type: PageTransitionType.fade,
          duration: const Duration(milliseconds: 0),
          reverseDuration: const Duration(milliseconds: 0),
          child: OrderDetailScreen(order: arguments['order'] as Order),
        );
      case AppRoutes.searchOrderScreen:
        return CupertinoPageRoute(builder: (_) => const SearchOrderScreen());

      case AppRoutes.paymentComplete:
        return PageTransition(
          type: PageTransitionType.rightToLeft,
          duration: const Duration(milliseconds: 0),
          reverseDuration: const Duration(milliseconds: 0),
          child: const PaymentCompleteWidget(),
        );

      case AppRoutes.orderDetail:
        return PageTransition(
          type: PageTransitionType.rightToLeft,
          duration: const Duration(milliseconds: 0),
          reverseDuration: const Duration(milliseconds: 0),
          child: const OrderDetailWidget(),
        );

      case AppRoutes.productImagePreview:
        final arguments = settings.arguments as Map<String, dynamic>;
        return PageTransition(
          type: PageTransitionType.fade,
          child: PhotoFullScreenBasic(galleries: arguments['galleries'] as List<PDD.Gallery>, startAtIndex: arguments['startAtIndex'] as int),
        );
      /* builder: (_) => ProductImagePreview(
                  galleries: arguments['galleries'] as List<Gallery>,
                  startAtIndex: arguments['startAtIndex'] as int,
                )); */

      case AppRoutes.myCouponScreen:
        return CupertinoPageRoute(builder: (_) => const MyCouponScreen());
      case AppRoutes.shopChillSearchSuggestionShops:
        final arguments = settings.arguments as Map<String, dynamic>? ?? {};
        final bool argsfromAffiliate = arguments['fromAffiliate'] as bool? ?? false;
        return PageTransition(
          type: PageTransitionType.fade,
          child: ShopChillSearchSuggestionShops(fromAffiliate: argsfromAffiliate),
        );
      case AppRoutes.affiliateProductDetailScreen:
        final arguments = settings.arguments as Map<String, dynamic>? ?? {};
        return PageTransition(
          type: PageTransitionType.rightToLeft,
          duration: const Duration(milliseconds: 100),
          reverseDuration: const Duration(milliseconds: 100),
          child: AffiliateProductDetailScreen(
            slug: arguments['slug'] as String,
            categoryId: arguments['categoryId'] as int,
            shopId: arguments['shopId'] as int,
            productId: arguments['productId'] as int,
            isFromRecommendTab: arguments['isFromRecommendTab'] as bool? ?? false,
          ),
        );
      case AppRoutes.affiliateShopDetailScreen:
        final arguments = settings.arguments as Map<String, dynamic>? ?? {};
        return PageTransition(
          type: PageTransitionType.rightToLeft,
          duration: const Duration(milliseconds: 100),
          reverseDuration: const Duration(milliseconds: 100),
          child: AffiliateShopDetailScreen(shopId: arguments['shopId'] as int),
        );
      /* case AppRoutes.affiliateProductDetailScreen:
        return CupertinoPageRoute(
          builder: (_) {
            final arguments = settings.arguments as Map<String, dynamic>? ?? {};
            return AffiliateProductDetailScreen(
              slug: arguments['slug'] as String,
              categoryId: arguments['categoryId'] as int,
              shopId: arguments['shopId'] as int,
              productId: arguments['productId'] as int,
              isFromRecommendTab: arguments['isFromRecommendTab'] as bool? ?? false,
            );
          },
        );
      case AppRoutes.affiliateShopDetailScreen:
        return CupertinoPageRoute(
          builder: (_) {
            final arguments = settings.arguments as Map<String, dynamic>? ?? {};
            return AffiliateShopDetailScreen(shopId: arguments['shopId'] as int);
          },
        ); */
      case AppRoutes.revenueDetailScreen:
        return CupertinoPageRoute(
          builder: (_) {
            final arguments = settings.arguments as Map<String, dynamic>? ?? {};
            return RevenueDetailScreen(billing: arguments['billing'] as DataBillings);
          },
        );

      case AppRoutes.socialPlatformScreen:
        return CupertinoPageRoute(builder: (_) => const SocialPlatformScreen());

      case AppRoutes.addSocialPlatformScreen:
        final arguments = settings.arguments as List<DataPlatformSocial>? ?? [];
        return CupertinoPageRoute(builder: (_) => AddSocialPlatformScreen(platforms: arguments));

      case AppRoutes.affiliateTermOfServicePage:
        return CupertinoPageRoute(builder: (_) => const AffiliateTermOfServicePage());

      case AppRoutes.addressSelectionPage:
        final arguments = settings.arguments as Map<String, dynamic>? ?? {};
        return CupertinoPageRoute(builder: (_) => AddressSelectionPage(standardAddressModel: arguments['standardAddressModel'] as StandardAddressModel?));

      default:
        // Handle unknown routes
        return CupertinoPageRoute(
          builder: (_) => Scaffold(body: Center(child: Text('No route defined for ${settings.name}'))),
        );
    }
  }
}
